<template>
	<custom-page>
		<view class="container">
				<image :src="codeImage" style="width: 400rpx;height: 400rpx;" @click="previewImg()" />
		</view>
	</custom-page>
</template>

<script>
	export default {
		data() {
			return {
				access_token: "",
				codeImage: "",
				appid: "wx4b17a04ec8915942",
				secret: "533dc089b007b5ce9a267141e78730c2"
			};
		},
		methods: {
			getAccessToken() {
				return new Promise((resolve, reject) => {
					let that = this
					uni.request({
						// https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=你的APPID&secret=你的微信小程序密钥
						url: 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wx4b17a04ec8915942&secret=533dc089b007b5ce9a267141e78730c2',
						method: "GET",
						success(res) {
							that.access_token = res.data.access_token;
							resolve(res.data.access_token)
						},
						fail(err) {
							reject(err)
							console.log(err)
						}
					})
				})
			},
			getQRCode(key, version) {
				let that = this
				uni.request({
					url: `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${this.access_token}`,
					responseType: "arraybuffer",
					method: "POST",
					data: {
						// 更多参数请看官方文档
						"page": "pages/pages-tabs/home/<USER>", // 默认是主页，页面 page
						"scene": 'sourceType=1', // 参数
						"check_path": false, // 默认是true，检查page 是否存在
						"env_version": 'release', // 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
					},
					success(res) {
						// 该接口返回的是 图片二进制 数据
						const arrayBuffer = new Uint8Array(res.data)
						const base64 = uni.arrayBufferToBase64(arrayBuffer)
						that.codeImage = `data:image/jpeg;base64,${base64}`
						// console.log(base64);
					},
					fail(err) {
						console.log(err)
					}
				})
			},
			previewImg(url) {
				uni.previewImage({
					urls: [this.codeImage],
				});
			}
		},
		onLoad() {
			this.getAccessToken().then(res => {
				this.getQRCode()
			})
		}
	}
</script>

<style>
	.container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100vh;
	}
</style>