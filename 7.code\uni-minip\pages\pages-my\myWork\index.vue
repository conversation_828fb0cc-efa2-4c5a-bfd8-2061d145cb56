<template>
	<custom-page bg="bg_head_pro.png">
	    <custom-header title="我的作品" bg-color="transparent"></custom-header>
		<view class="worker-list">
			<view class="worker-item tap-style" v-for="item in wokerList" :style="{ backgroundImage: `url(${IMG_URL + item.bg})` }" @click="goPath(item.path)">
				<view class="title-box">
					<view class="title">
						<text v-for="(char, index) in item.title" :key="index" class="title-char">{{ char }}</text>
					</view>
					<view class="icon-box">
						<image class="icon" :src="IMG_URL + 'AI_icon.png'" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</view>
	</custom-page>
</template>

<script>
	import { IMG_URL } from '@/utils/config';
	export default {
		data() {
			return {
				IMG_URL,
				wokerList: [
					{
						title: '智笔生花',
						bg: 'bg_zbsh.png',
						path: '/pages/pages-my/talkOnTheWall/index?isMyGo=true'
					},
					{
						title: '唐宋画境',
						bg: 'bg_tshj.png',
						path: '/pages/pages-my/tangsongDynastyPaintings/index'
					}
				]
			};
		},
		onLoad(opts) {
			console.log('opts', opts)
		},
		methods: {
			goPath(path) {
				if (!path) return;
				uni.navigateTo({
					url: path
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.worker-list {
		padding: 20rpx;
		margin-top: 30rpx;
	}
	.worker-item {
		height: 327rpx;
		background-size: 100%;
		background-position: center;
		background-repeat: no-repeat;
		display: flex;
		align-items: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		&+.worker-item {
			margin-top: 20rpx;
		}
		.title-box {
			width: 100rpx;
			height: 260rpx;
			@include bgUrl('my_work_item_title_bg.png');
			background-size: cover;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 8rpx;
			margin-left: 60rpx;
			.title {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 8rpx;
				color: #000;
				.title-char {
					writing-mode: vertical-lr;
					line-height: 1.2;
				}
			}
			.icon {
				width: 66rpx;
				height: 36rpx;
			}
		}
	}
</style>
