<template>
	<view class="pickbox" v-show="pickShow">
		<view class="pickbox-bottom">
			<view class="pb-box">
				<view class="pbox-l" @click="quxiao">取消</view>
				<view class="pbox-r" @click="submit">确定</view>
			</view>
			<view class="content">
				<view class="child" :style="indexs==index?'color: #B36859':'color:#666363'" v-for="(item,index) in picklist" @click="label(item,index)">{{item.label}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:'picker',
		props:['picklist'],
		data() {
			return {
				pickShow:false,
				indexs:-1,
				info:{
					label:'',
					value:''
				}
			};
		},
		methods:{
			//确定
			submit(){
				if(this.info.label==''){
					uni.$u.toast('请选择')
				}else if(this.info.label!=''){
					this.$emit('echo',this.info)
					this.pickShow = false
				}
			},
			//取消
			quxiao(){
				this.pickShow = false
			},
			//点击标签
			label(item,index){
				this.indexs = index
				this.info.label = item.label
				this.info.value = item.value
			},
			shows(){
				this.pickShow = true
			}
		},
		onLoad(){
			
		},
		onShow(){
			
		}
	}
</script>

<style lang="scss" scoped>
	.pickbox{
		position: fixed;
		top: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 99998;
		.pickbox-bottom{
			position: fixed;
			bottom: 0;
			width: 100%;
			// height: 368rpx;
			min-height: 168rpx;
			background: #FFFFFF;
			animation-name: ball;
			animation-duration: 0.1s;
			.pb-box{
				width: 100%;
				height: 80rpx;
				border-bottom: 1rpx solid #EEE9E1;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.pbox-l{
					margin-left: 30rpx;
					font-weight: 400;
					font-size: 30rpx;
					color: $tip-font-color;
				}
				.pbox-r{
					margin-right: 30rpx;
					font-weight: 400;
					font-size: 30rpx;
					color: $sec-font-color;
				}
			}
			.content{
				width: calc(100% - 60rpx);
				margin-left: 30rpx;
				margin-bottom: 88rpx;
				.child{
					width: 100%;
					height: 100rpx;
					border-bottom: 1rpx solid #EEE9E1;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
	@keyframes ball {
	    0% { bottom: -368rpx; }
	    25% { bottom: -276rpx; }
	    50% { bottom: -184rpx; }
	    75% { bottom: -92rpx; }
	    100% { bottom: 0rpx; } 
	}

</style>