import {
	request
} from '@/utils/request.js';

export const getConfirmOrderDetail = (data) => {
	return request({
		url: 'museum-app/order/payDetail',
		method: 'get',
		data
	});
};

// museum-app/order/prepay
export const payOrder = (data) => {
	return request({
		url: 'museum-app/order/prepay',
		method: 'post',
		data
	});
};
export const prepayByBestPay = (data) => {
	return request({
		url: 'museum-app/order/prepayByBestPay',
		method: 'post',
		data
	});
}

// 订单列表 museum-app/order/orderList
export const getOrderList = (data) => {
	return request({
		url: 'museum-app/order/orderList',
		method: 'get',
		data
	});
};
// 订单详情 museum-app/order/orderDetail
export const getOrderDetail = (data) => {
	return request({
		url: 'museum-app/order/orderDetail',
		method: 'get',
		data
	});
};
// 取消订单 museum-app/order/closeOrder
export const closeOrder = (data) => {
	return request({
		url: 'museum-app/order/closeOrder',
		method: 'get',
		data
	});
};
// 删除订单 museum-app/order/deleteOrder
export const delOrder = (data) => {
	return request({
		url: 'museum-app/order/deleteOrder',
		method: 'get',
		data
	});
};

// 申请退款详情 museum-app/order/checkCancelOrder
export const checkCancelOrder = (data) => {
	return request({
		url: 'museum-app/order/checkCancelOrder',
		method: 'get',
		data
	});
};

// 提交退款 museum-app/order/cancelOrder
export const orderRefund = (data) => {
	return request({
		url: 'museum-app/order/cancelOrder',
		method: 'get',
		data
	});
};
// 退款进度 museum-app/order/cancelOrderProgress
export const refundProgress = (data) => {
	return request({
		url: 'museum-app/order/cancelOrderProgress',
		method: 'get',
		data
	});
};

//获取模板id
export const getTmplIds = (data) => {
	return request({
		url: 'museum-app/message/getTemplateIds',
		method: 'get',
		data
	});
};