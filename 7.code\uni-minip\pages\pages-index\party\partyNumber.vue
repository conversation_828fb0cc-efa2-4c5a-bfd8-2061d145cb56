<template>
	<custom-page class="page" bg="party_top.png" :empty="!partyList.length">
		<custom-header title="排号" :navheight.sync="navHeight" bg-color="transparent">
			<template slot="bottom">
				<view class="top-tab">
					<view :class="[index==selectIndex ?'tab-item select' : 'tab-item']" v-for="(item,index) in tabList"
						:key="index" @click="selectTab(index)">
						{{item.name}}
					</view>
				</view>
			</template>
		</custom-header>
		<view class="box">
			<scroll-view scroll-y refresher-enabled :style="{'height':`calc(100vh - ${navHeight}px - 150rpx)`}"
				:refresher-triggered="triggered" @refresherrefresh="onRefresh" v-if="partyList.length">
				<view class="party-item" v-for="(item,index) in partyList" :key="index">
					<view class="card-title">
						《梦回大唐：奇幻时空之旅》
					</view>
					<view class="party-content" v-if="selectIndex==0">
						<view class="party-text">
							共有<text class="bold-text">{{item.sumCount}}</text>人在排队
						</view>
						<view class="party-text">
							当前号码为<text class="bold-text">{{item.currentNumber}}</text>
						</view>
					</view>
					<view class="sub-card">
						<view class="sub-text">
							您的号码
						</view>
						<view class="sub-text-center">
							{{item.myNumber}}
						</view>
						<view class="sub-text">
							<template v-if="selectIndex==0">
								前面共有<text class="bold-text">{{item.waitNumber}}</text>位游客等待
							</template>
							<template v-if="selectIndex==1">
								排队号码已过期，请重新排队
							</template>
						</view>
					</view>
				</view>		
			</scroll-view>
		</view>
		<view class="bottom-box" v-if="selectIndex==0&&partyList.length">
			<custom-button type="info" small @click="cancelQueue">取消排队</custom-button>
		</view>
	</custom-page>
</template>

<script>
	import {
		getQueue,
		cancelQueue
	} from '@/api/party.js';
	export default {
		data() {
			return {
				navHeight: 44,
				statusBarHeight: 0,
				selectIndex: 0,
				tabList: [{
						name: "当前号码",
					},
					{
						name: "历史号码"
					}
				],
				partyList: [],
				triggered: false
			};
		},
		computed: {

		},
		methods: {
			selectTab(e) {
				this.selectIndex = e;
				this.getQueueList(true)
			},
			getQueueList(init) {
				getQueue(this.selectIndex + 1).then(res => {
					if (init) {
						this.partyList = []
					}
					if (this.selectIndex == 0&&res.queueStatus!=1) {
						this.partyList = [res]
					}
					if (this.selectIndex == 1) {		
						res.forEach((element, index) => {
							this.partyList.push({
								myNumber: element
							})
						})
					}
					if (this.triggered) {
						setTimeout(() => {
							this.triggered = false
						}, 500)

					}
				})
			},
			cancelQueue() {
				let that=this
				uni.showModal({
					content: '是否要取消排队',
					confirmText: "是",
					cancelText: "否",
					success: function(res) {
						if (res.confirm) {
							cancelQueue().then(res => {
								uni.showToast({
									title: "取消成功",
									icon: 'none'
								})
								that.$emit('type')
							})
						} else if (res.cancel) {}
					}
				});
			},
			onRefresh() {
				this.triggered = true;
				this.getQueueList(true)
			},
			initData(res) {
				if (res!=null) {
					this.selectIndex = 0
					this.partyList = [res]
				}
				 else{
					 this.getQueueList()
				 }
			}
		},
		mounted() {
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		height: 100vh;
	}

	.top-tab {
		padding: 0 50rpx;
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid #D0C9BC;

		.tab-item {
			flex: 1;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			color: #666363;

			&.select {
				font-weight: bold;
				font-size: 30rpx;
				color: #1A1818;
				position: relative;
			}

			&.select::before {
				position: absolute;
				content: "";
				bottom: 0;
				left: calc(50% - 30rpx);
				width: 60rpx;
				height: 5rpx;
				background: #B36859;

			}

		}
	}

	.box {
		width: calc(100% - 50rpx);
		margin: 20rpx 25rpx;
		display: flex;
		flex-direction: column;

		.party-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 20rpx;
			@include bgUrl('party_list_card.png');
			background-size: 100% 100%;
			padding: 20rpx;

			.card-title {
				width: calc(100% - 40rpx);
				margin: 20rpx;
				height: 100rpx;
				font-weight: bold;
				font-size: 32rpx;
				color: #1A1818;
				display: flex;
				align-items: center;
				justify-content: center;
				border-bottom: 1rpx solid #D0C9BC;
			}

			.party-content {
				margin-bottom: 20rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.party-text {
					font-weight: 400;
					font-size: 26rpx;
					color: #666363;
					line-height: 50rpx;

					.bold-text {
						font-weight: bold;
						font-size: 30rpx;
						color: #666363;
						margin: 0 10rpx;

					}
				}
			}

			.sub-card {
				box-sizing: border-box;
				margin: 10rpx 0 0 2rpx;
				width: 100%;
				@include bgUrl('party_sub_card.png');
				background-size: 100% 100%;
				height: 300rpx;
				z-index: 2;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 60rpx 0;

				.sub-text {
					font-weight: bold;
					font-size: 30rpx;
					color: #1A1818;
				}

				.sub-text-center {
					font-weight: bold;
					font-size: 32rpx;
					color: #B36859;
					margin: 20rpx 0 30rpx 0;
				}
			}
		}
	}

	.bottom-box {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		background: #FCFBF7;
		box-shadow: 0rpx -3rpx 10rpx 0rpx rgba(242, 241, 240, 0.35);
		height: 112rpx;
		padding-bottom: env(safe-area-inset-bottom);
		padding-right: 30rpx;
	}
</style>