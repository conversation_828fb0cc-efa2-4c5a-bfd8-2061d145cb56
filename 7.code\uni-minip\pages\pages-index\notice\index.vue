<template>
	<custom-page :bg-top="navHeight" :empty="feedlist.length === 0">
		<custom-header title="公告" :navheight.sync="navHeight" show-loading></custom-header>
		<u-loading-icon class="loading"  :show="show" text="加载中" textSize="18"></u-loading-icon>
		<!-- 有数据 -->
		<view class="feedlist">
			<view class="scroll">   				    
				<view class="feedlists" v-for="(item,index) in feedlist" @click="details(item)">
					<view class="feedlist-box"  >
						<view class="feedbox-title">
							<view class="title-l">{{item.noticeTitle}}</view>
						</view>
						<view class="feedbox-content">
							{{item.noticeContent | noticeContent}}
						</view>
						<view class="feedbox-time">
							{{item.createTime}}
						</view>	
					</view>
				</view>
			</view>
		</view>
	</custom-page>
</template>

<script>
	import {noticeList} from "@/api/home.js"
	export default {
		data() {
			return {
				show:true,
	            navHeight:0,
				feedlist:[],
				pageNum:'1',
				pageSize:'10',
				total:'0',
			};
		},
		filters:{
			noticeContent(cellValue){
			  const html = cellValue
			  const relStyle = /style\s*?=\s*?([‘"])[\s\S]*?\1/g //去除样式
			  const relTag = /<.+?>/g //去除标签
			  const relClass = /class\s*?=\s*?([‘"])[\s\S]*?\1/g // 清除类名
			  let newHtml = ''
			  if (html) {
				newHtml = html.replace(relStyle, '')
				newHtml = newHtml.replace(relTag, '')
				newHtml = newHtml.replace(relClass, '')
			  }
			  return newHtml
			}
		},
		created(){
			this.show = false
			this.getlist()
		},
		onShow(){

		},
		onReachBottom(){
			if(this.pageNum*this.pageSize<this.total){
				this.pageNum++
				this.getlist()
			}
		},
		onPullDownRefresh(v){
			this.pullDown()		
		},
		methods:{
			async getlist(){
				  let data = {
					  pageNum:this.pageNum,
					  pageSize:this.pageSize
				  }
				  const { rows,total } = await noticeList(data);
				  this.total = total
				  if(total <= '10'){
					  this.feedlist = rows
				  }else if(total > '10'){
					  this.feedlist = [...this.feedlist,...rows]
				  } 
			},
			pullDown(){
				this.pageNum = '1'
				this.feedlist = []
				this.getlist()
				setTimeout(()=>{
					uni.stopPullDownRefresh()
				},400)
			},
			details(item){
				uni.navigateTo({
					url:'/pages/pages-index/notice/details?item='+ encodeURIComponent(JSON.stringify(item))
				})
			},
		}
	}
</script>

<style lang="scss">
.feedlist{
	// width: calc(100% - 40rpx);
	// margin: 20rpx;
	padding: 0 20rpx;
	.scroll{
		// margin-bottom: 300rpx;
		.feedlists{
			z-index: 99999;
			.feedlist-box{
				width: 100%;
				height: 200rpx;
				@include bgUrl('feedback-box.png');
				background-size: 100% 100%;
				position: relative;
				overflow: hidden;
				margin-bottom: 20rpx;
				.feedbox-title{
					width: calc(100% - 68rpx);
					height: 28rpx;
					line-height: 28rpx;
					margin-left: 34rpx;
					margin-top: 40rpx;
					margin-bottom: 20rpx;
					display: flex;
					align-items: center;
					justify-content:space-between;
					.title-l{
						font-weight: bold;
						font-size: 28rpx;
						color: $main-font-color;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			    .feedbox-content{
					width: calc(100% - 68rpx);
					height: 28rpx;
					line-height: 28rpx;
					margin-left: 34rpx;
					white-space: nowrap;    
					overflow: hidden;        
					text-overflow: ellipsis; 
					margin-bottom: 29rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: $tip-font-color;
				}
				.feedbox-time{
					margin-left: 34rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: $tip-font-color;
					font-style: normal;
				}
			}
			
		}
	}
}

// 加载动画
::v-deep .u-loading-icon{
	height: 80vh;
}
</style>
