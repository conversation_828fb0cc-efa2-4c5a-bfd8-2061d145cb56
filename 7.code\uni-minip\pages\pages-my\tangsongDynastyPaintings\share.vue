<template>
	<custom-page bg="bg_head_pro.png">
		<custom-header title="分享" :scroll-top="scrollTop" :scroll-ratio="2"></custom-header>
		<view class="tshj_share">
			<view class="main-bg">
				<image class="person-image" :src="imageUrl" />
				<view class="share-content">
					<text class="share-txt">
						前往辽宁省博物馆二层西侧，\n唐宋风华数字展厅，\n解锁同款古风图片。
					</text>
					<view class="share-qrcode">
						<image src="https://baconmockup.com/83/83" />
						<text class="share-qrcode-txt">长按二维码\n识别</text>
					</view>
				</view>
			</view>

			<view class="share-btn">
				<view class="share-btn-item tap-style" @click="onShareFriend">
					<image :src="IMG_URL + 'tshj_share_icon_wechat.png'" />
					<view>分享给好友</view>
				</view>
				<view class="share-btn-item tap-style">
					<image :src="IMG_URL + 'tshj_share_icon_wechatmoments.png'" />
					<view>分享朋友圈</view>
				</view>
			</view>
		</view>
	</custom-page>
</template>

<script>
import { IMG_URL } from '@/utils/config'
export default {
	data() {
		return {
			IMG_URL,
			scrollTop: 0,
			imageUrl: ''
		};
	},
	onLoad(options) {
		// 获取传递过来的图片地址参数
		if (options.imageUrl) {
			this.imageUrl = decodeURIComponent(options.imageUrl);
		}
	},
	methods: {
		onShareFriend() {
			uni.downloadFile({
				url: this.imageUrl,
				success: (res) => {
					
					if (res.statusCode === 200) {
						uni.showShareImageMenu({
							path: res.tempFilePath,
							success: res => {
								console.log(res)
							},
							fail: err => { 
								console.log(err)
							}
						})
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				},
				fail: () => {
					
				}
			});
		}
	},
	onPageScroll({ scrollTop }) {
		this.scrollTop = scrollTop;
	},
}
</script>

<style lang="scss" scoped>
.tshj_share {
	padding: 80rpx 30rpx 0;
	.main-bg {
		margin: 0 auto;
		width: 672rpx;
		height: 994rpx;
		@include bgUrl("tshj_share_poster_bg.png");
		background-size: 100% 100%;
		display: flex;
		align-items: flex-end;
		box-sizing: border-box; 
		position: relative;
		padding-bottom: 82rpx;
		.person-image {
			width: 479rpx;
			height: 720rpx;
			position: absolute;
			top: 84rpx;
			left: 50%;
			transform: translateX(-50%);
			z-index: -1;
		}
		.share-content {
			height: 82rpx;
			width: 480rpx;
			display: flex;
			font-size: 20rpx;
			color: #121212;
			line-height: 28rpx;
			margin: 0 auto;
			font-weight: bold;
			.share-txt {
				flex: 1;
				flex-shrink: 1;
			}
			.share-qrcode {
				display: flex;
				align-items: center;
				gap: 8rpx;
				padding-left: 24rpx;
				position: relative;
				&::before {
					content: '';
					width: 1rpx;
					height: 80rpx;
					background: #4A3006; 
					position: absolute;
					top: 50%;
					left: 0;
					transform: translateY(-50%);
				}
				image {
					width: 83rpx;
					height: 83rpx;
					border-radius: 10rpx;
				}
			}
		}
	}
	.share-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 162rpx;
		padding: 64rpx 170rpx 0;
		.share-btn-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #1A1818;
			white-space: nowrap;
			image {
				width: 110rpx;
				height: 110rpx;
			}
			view {
				line-height: 1;
				padding-top: 20rpx;
			}
		}
	}
}
</style> 