<template>
	<view class="custom-cell-list">
		<view class="custom-cell tap-style" v-for="(item, index) in dataParse" :key="index" @click="onCellClick(item)">
			<view class="left">
				<image :src="IMG_URL + item.icon" class="cell_icon"></image>
				{{ item.title }}
			</view>
			
			<view class="right">
				<slot :cell="item"><view>{{ item.value }}</view></slot>
				<image v-if="!item.noArrow" :src="IMG_URL + '<EMAIL>'" class="arrow_right-icon"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import { IMG_URL } from '@/utils/config'
	export default {
		props: {
			data: Array,
			title: String,
			icon: String
		},
		data() {
			return {
				IMG_URL
			}
		},
		computed: {
			dataParse() {
				return this.data.map(item => {
					item.value = item.value || ''
					return item
				})
			}
		},
		methods: {
			onCell<PERSON>lick(cell) {
				this.$emit('click', cell)
			}
		}
	}
</script>

<style scoped lang="scss">
	.custom-cell {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		background-color: #fff;
		height: 100rpx;
		font-weight: 400;
		font-size: 28rpx;
		 > view {
			 display: flex;
			 align-items: center;
			 gap: 20rpx;
		 }
		.left {
			.cell_icon {
				width: 48rpx;
				height: 48rpx;
			}
		}
		.right {
			.arrow_right-icon {
				width: 50rpx;
				height: 50rpx;
			}
		}
		&+.custom-cell {
			position: relative;
			&:before {
				display: block;
				content: '';
				width: calc(100% - 60rpx);
				height: 1rpx;
				background: #D0C9BC;
				position: absolute;
				top: 0;
				left: 30rpx;
			}
		}
	}
</style>