<template>
	<custom-page bg="confirm_order_bg.png">
		<custom-header title="确认订单" bg-color="transparent"></custom-header>

		<view class="confirm_order_card">
			<view class="status">
				{{ details.orderStatusText }}
				<view class="status-label">支付剩余时间</view>
				<u-count-down :time="details.milliSecond" format="mm:ss"></u-count-down>
			</view>
			<view class="card_content">
				<view class="title">{{ details.exhibitionName }}</view>
				<view class="price">
					<view>￥ {{ details.totalAmount }}</view>
					需支付
				</view>
				<view class="text_left">
					<view class="text_item">
						<view>参观人数</view>
						<view>{{ details.visitorsNum }}</view>
					</view>
					<view class="text_item">
						<view>参观日期</view>
						<view>{{ details.admissionDateStr }}</view>
					</view>
					<view class="text_item">
						<view>下单时间</view>
						<view>{{ details.addTime }}</view>
					</view>
				</view>
			</view>
		</view>
		<custom-bottombar align-right>
			<custom-button type="info" class="mr30" @click="backHome">返回首页</custom-button>
			<custom-button type="primary" @click="handlePay">立即支付</custom-button>
		</custom-bottombar>
	</custom-page>
</template>

<script>
	import {
		getConfirmOrderDetail,
		payOrder
	} from '@/api/order';
	import {
		payment
	} from '@/utils';
	export default {
		data() {
			return {
				orderSn: '',
				details: {},
				payShow: false
			};
		},
		onLoad({
			orderSn
		}) {
			this.orderSn = orderSn
			this.init()
		},
		onShow() {
			if (this.payShow) {
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/pages-my/order/details?orderSn=' + this.orderSn + '&pageType=1'
					})
				}, 100)
			}
		},
		methods: {
			async init() {
				const data = await getConfirmOrderDetail({
					orderSn: this.orderSn
				})
				this.details = data
			},
			backHome() {
				this.$store.commit("USER_ADD", [])
				uni.navigateBack({
					delta: 999
				})
			},
			async handlePay() {
				await payment(this.orderSn)
				this.payShow = true
			}
		}
	}
</script>

<style lang="scss" scoped>
	.confirm_order_card {
		margin-top: 42rpx;
		height: 1000rpx;
		@include bgUrl("confirm_order_card.png");
	}

	.status {
		padding: 69rpx 0 0 105rpx;
		font-weight: bold;
		font-size: 34rpx;
		display: flex;
		align-items: flex-end;
		line-height: 1;

		.status-label {
			font-weight: 400;
			font-size: 26rpx !important;
			margin-left: 26rpx;
		}

		::v-deep .u-count-down__text {
			@extend .status-label;
			margin: 0;
			line-height: inherit !important;
		}
	}

	.card_content {
		padding-top: 165rpx;
		text-align: center;

		.title {
			font-weight: bold;
			font-size: 34rpx;
		}

		.price {
			padding-top: 132rpx;
			font-weight: 400;
			font-size: 26rpx;
			line-height: 1;

			>view {
				padding-bottom: 30rpx;
				font-weight: bold;
				font-size: 34rpx;
				color: $sec-font-color;
			}
		}

		.text_left {
			text-align: left;
			margin-left: 140rpx;
			line-height: 1;
			margin-top: 60rpx;

			.text_item {
				display: flex;
				align-items: center;
				gap: 38rpx;
				font-weight: 400;
				font-size: 26rpx;
				margin-bottom: 39rpx;
			}
		}
	}
</style>