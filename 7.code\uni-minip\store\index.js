// 页面路径：store/index.js 
import Vue from 'vue';
import Vuex from 'vuex';
import { login, getUserInfo, getWriteOffStatus } from '@/api/login.js';
import { IMG_URL } from '@/utils/config';

Vue.use(Vuex);

//Vuex.Store 构造器选项
const store = new Vuex.Store({
    state: {
        logo: IMG_URL + 'logo.png',
        token: uni.getStorageSync("token"),
        userInfo: {},
        userSelect: [],//已选择的联系人
		selectedTickets: [], // 门票预订的门票
		sourceInfo: uni.getStorageSync('sourceInfo'), // 来源
    },
    getters: {
        source: state => state.source
    },
    mutations: {
        SET_TOKEN (state, token) {
            state.token = token;
            if (token) {
                uni.setStorageSync('token', token);
            } else {
                // uni.clearStorageSync();
				uni.removeStorageSync('token')
            }
        },
        SET_USER_INFO (state, data) {
            state.userInfo = data;
        },
        // 已选择的联系人
        USER_ADD (state, data) {
            state.userSelect = data;
        },
		SET_TICKETS (state, data) {
		    state.selectedTickets = data;
		},
		SET_SOURCE (state, data) {
			state.source = data;
		},
		SET_SOURCE_INFO (state, data) {
			state.sourceInfo = data;
		}
    },
    actions: {
        async LOGIN ({ commit, dispatch }, phoneCode) {
            return new Promise((resolve, reject) => {
                uni.showLoading({
                    title: '登录中...'
                });
                uni.login({
                    success: async ({ code }) => {
                        try {
                            const { access_token } = await login({
                                code,
                                phoneCode
                            });
                            commit('SET_TOKEN', access_token);
                            dispatch("GET_USER_INFO");
                            uni.hideLoading();
                            resolve();
                        } catch {
                            reject();
                        }
                    },
                    fail: () => {
                        reject();
                    }
                });
            });
        },
        async GET_USER_INFO ({ commit }) {
            const data = await getUserInfo();
            const writeOffStatus = await getWriteOffStatus();
            commit('SET_USER_INFO', {
				...data,
				writeOffStatus: writeOffStatus.data || writeOffStatus // 是否有核销的权限
			});
        },
        LOGOUT ({ commit }) {
            console.log("退出登录");
            commit("SET_TOKEN", "");
            commit("SET_USER_INFO", {});
        }
    }
});
export default store;
