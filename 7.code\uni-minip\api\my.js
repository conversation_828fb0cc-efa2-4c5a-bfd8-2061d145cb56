
import { request } from '@/utils/request.js';

//添加意见反馈
export const feedbackAdd = (data) => {
    return request({
        url: 'museum-app/feedback/save',
        method: 'post',
        data
    });
};
//意见反馈列表
export const feedbackList = (data) => {
    return request({
        url: 'museum-app/feedback/my-feedbacks',
        method: 'get',
        data: data
    });
};
//获取客服电话  
export const phoneNum = () => {
    return request({
        url: 'museum-app/contacts/servicePhone',
        method: 'get',
    });
};

// 头像修改
export const updateAvatar = (data) => {
    return request({
        url: 'museum-app/wx-user/updateAvatar',
        method: 'post',
        data
    });
};
// 新增联系人
export const saveUser = (data) => {
    return request({
        url: 'museum-app/contacts/save',
        method: 'post',
        data
    });
};
// 编辑联系人
export const updateUser = (data) => {
    return request({
        url: 'museum-app/contacts/update',
        method: 'post',
        data
    });
};
// 删除联系人
export const deleteItem = (data) => {
    return request({
        url: 'museum-app/contacts/delete',
        method: 'post',
        data
    });
};
// 联系人列表
export const indexList = (data) => {
    return request({
        url: 'museum-app/contacts/list',
        method: 'get',
        params: data
    });
};
// 发送验证码
export const getCodePhone = (data) => {
    return request({
        url: 'museum-app/wx-user/editPhone/SendCode?phonenumber=' + data.phonenumber,
        method: 'get',
    });
};
// 修改手机号提交btn
export const editIphone = (data) => {
    return request({
        url: 'museum-app/wx-user/edit-userPhone',
        method: 'post',
        data
    });
};
// 修改昵称
export const editName = (data) => {
    return request({
        url: 'museum-app/wx-user/updateName',
        method: 'post',
        data
    });
};
// 我的作品查询
export const getMyPro = (data) => {
    return request({
        url: 'museum-app/work/my-list',
        method: 'get',
        params: data
    });
};
// 我的作品保存
export const saveMyPro = (data) => {
    return request({
        url: 'museum-app/work/save?param=' + data,
        method: 'get',
        params: data
    });
};
// 删除我的作品
export const deleteWork = (data) => {
    return request({
        url: 'museum-app/work/' + data,
        method: 'delete',
    });
};
///门票核销-门票核销数量
export const writeNum = () => {
    return request({
        url: 'museum-app/order-admission/info',
        method: 'get',
    });
};
//门票核销-获取核销列表
export const writeList = (data) => {
    return request({
        url: 'museum-app/order-admission/list',
        method: 'get',
        data
    });
};
//门票核销-详情
export const writeDel = (data) => {
    return request({
        url: 'museum-app/order-admission/detail',
        method: 'post',
        data
    });
};
//门票核销-扫一扫
export const writeCode = (data) => {
    return request({
        url: 'museum-app/order-admission/scanCodeInfo',
        method: 'get',
        data
    });
};
//门票核销-核销
export const writeOff = (data) => {
    return request({
        url: 'museum-app/order-admission/writeOff',
        method: 'post',
        data
    });
};
//门票核销-核销记录详情
export const writeHisDetails = (data) => {
    return request({
        url: 'museum-app/order-admission/detail',
        method: 'post',
        data
    });
};
