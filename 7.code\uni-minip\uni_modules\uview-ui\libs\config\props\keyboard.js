/*
 * <AUTHOR> LQ
 * @Description  :
 * @version      : 1.0
 * @Date         : 2021-08-20 16:44:21
 * @LastAuthor   : LQ
 * @lastTime     : 2021-08-20 17:07:49
 * @FilePath     : /u-view2.0/uview-ui/libs/config/props/keyboard.js
 */
export default {
    // 键盘组件
    keyboard: {
        mode: 'number',
        dotDisabled: false,
        tooltip: true,
        showTips: true,
        tips: '',
        showCancel: true,
        showConfirm: true,
        random: false,
        safeAreaInsetBottom: true,
        closeOnClickOverlay: true,
        show: false,
        overlay: true,
        zIndex: 10075,
        cancelText: '取消',
        confirmText: '确定',
        autoChange: false
    }
}
