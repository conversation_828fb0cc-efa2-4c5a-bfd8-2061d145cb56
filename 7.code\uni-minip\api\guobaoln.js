import { request } from '@/utils/request.js';

// 获取国宝辽宁门票预订信息
export const getGblnTicketBookingInfo = () => {
    return request({
        url: 'museum-app/app/digitalShowroomManage/gb/info',
        method: 'get',
    });
};

// 新增联系人
export const saveGblnUser = (data) => {
    return request({
        url: 'museum-app/contacts/gb/save',
        method: 'post',
        data
    });
};
// 编辑联系人
export const updateGblnUser = (data) => {
    return request({
        url: 'museum-app/contacts/gb/update',
        method: 'post',
        data
    });
};

// 提交订单  museum-app/order/gb/submit
export const submitOrder = (data) => {
    return request({
        url: 'museum-app/order/gb/submit',
        method: 'post',
        data
    });
};