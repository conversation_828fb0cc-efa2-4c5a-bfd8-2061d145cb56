<template>
    <u-popup :show="show" :round="20" mode="bottom" @close="close" @open="open">
        <view class="card_container" :class="!isScrollY?'card-content':'card-content allBg'"
            :style="{'height':touchHeight}">
            <view @touchstart="touchStart($event,'1')" @touchend="touchEnd($event,'1')">
				<view class="content-top">
				    <view class="top-item" v-for="item in iconList" @click="goPage(item)">
				        <image class="top-icon" mode=scaleToFill :src="item.icon"></image>
				        <view class="top-label">{{item.name}}</view>
				    </view>
				</view>
				<view class="list-title">
				    <!-- - 精彩推荐 {{ lastScrollTop }} {{ tabbarHeight }} | {{ navHeight }} -->
				    - 精彩推荐
				</view>
			</view>
            <view class="scroll_parnet">
                <custom-empty v-show="indexListArr.length === 0" :text="emptyText"></custom-empty>
                <scroll-view class="recomm-scroll" :throttle="false" :scroll-y="isScrollY" :scroll-top="scrollTop"
                    @scrolltolower="scrollBottom" @scroll="scroll">
                    <view class="content-body" @touchstart="touchStart($event,'2')" @touchend="touchEnd($event,'2')">
                        <view class="body-item" v-for="item in indexListArr" @click="godetails(item.id)">
                            <image class="item-picture" mode="aspectFill" :src="item.selectedImg"></image>
                            <view class="item-tips">{{item.type==1?'文化探索':item.type==2?'攻略':'-'}}</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
    </u-popup>
</template>

<script>
import { IMG_URL } from '@/utils/config';
import { checkLogin } from '@/utils';
import { scanCode } from "@/api/home.js";
import { login } from '../../../../api/login';
export default {
    props: {
        navHeight: Number,
        tabbarHeight: Number,
        total: Number,
        indexListArr: Array
    },
    data () {
        return {
            emptyText: '暂无数据',
            search: {
                pageNum: 1,
                pageSize: 15,
            },
            //滚动条配置项
            scrollTop: 0,
            old: {
                scrollTop: 0
            },
            lastScrollTop: 0,
            show: false,
            threshold: 10, // 阈值，根据实际需要调整
            lastScrollTop: 0,
            iconList: [
                { icon: IMG_URL + 'icon_ticket.png', name: "门票预订", needLogin: true, path: '/pages/pages-index/ticketBooking/ticketBooking' },
                { icon: IMG_URL + 'icon_earth.png', name: "文化探索", path: '/pages/pages-index/culturalExploration/index' },
                { icon: IMG_URL + 'icon_interaction.png', name: "扫码互动", needLogin: true, path: 'none' },
                { icon: IMG_URL + 'icon_map.png', name: "地图导览", path: '/pages/pages-index/mapGuide/mapGuide' },
                { icon: IMG_URL + 'icon_play.png', name: "观展须知", path: '/pages/pages-index/travelNotice/index' },
                { icon: IMG_URL + 'icon_notice.png', name: "展厅公告", path: '/pages/pages-index/notice/index' },
                // { icon: IMG_URL + 'icon_shop.png', name: "文创商城" },
                // { icon: IMG_URL + 'icon_activity.png', name: "活动报名" },
				 // { icon: IMG_URL + 'icon_VR.png', name: "VR排号",path:'/pages/pages-index/party/index' }
            ],
            touchSatartX: 0,
            touchSatartY: 0,
            touchHeight: '972rpx',//card高度
            touchCount: 0,//下滑
            upCount: 0,//上滑
            isScrollY: false,//是否允许滚动
            isAtTop: false,//是否顶部
            newE: null,
            isMove: false,//是否允许滑动
            changeScroll: '',//向上滑动还是向下滑动 （滚动部分）
            oldList: [],//旧数组

        };
    },
    onLoad () {
    },
    mounted () {
    },
    methods: {
        open () {
            // console.log('open');
            this.show = true;
            this.touchHeight = '972rpx';
            this.isScrollY = false;
            this.search = {
                pageNum: 1,
                pageSize: 15,
            };
        },
        // 跳转详情
        godetails (value) {
            uni.navigateTo({
                url: '/pages/pages-index/culturalExploration/details?infoId=' + value
            });
        },
        close () {
            this.show = false;
            this.$emit('update:showToatal', false);
            this.search = {
                pageNum: 1,
                pageSize: 15,
            };
            this.oldList = [];
            this.$emit('getList', this.search);
        },
        // 页面跳转
        goPage (item) {
            // console.log("点击");
			if (!item.path) {
				uni.showToast({
					title: '开发中...',
					icon: 'none'
				})
				return
			}
            if (item.needLogin && !checkLogin()) return; // 提前检测是否登录，不需要的话就删除。
            if (item.path == 'none') {
                //展览互动
                this.scanCode();
            } else {
                if (!item.path) return;
                uni.navigateTo({
                    url: item.path
                });
            }
        },
        //扫一扫
        scanCode () {
            // 允许从相机和相册扫码
            uni.scanCode({
                // scanType: ['QR_CODE'], //条形码
                success: function (res) {
                    // console.log('条码类型：' + res.scanType);
                    // console.log('条码内容：' + res.result);
                    // 微信小程序
                    if (res.errMsg == "scanCode:ok") {
                        // 扫描到的信息
                        let params = {
                            loginCode: res.result.substring(13,res.result.length-2)
                        };
                        console.log(params, 'params');
                        scanCode(params).then((res) => {
                           uni.$u.toast(res.msg)
                        });
                    } else {
                        console.log("未识别到二维码，请重新尝试！");
                    }
                }
            });
        },
        scroll (e) {
            // console.log(e.detail.scrollTop);
            const scrollTop = e.detail.scrollTop;
            this.lastScrollTop = scrollTop; // 更新滚动位置
        },
        scrollBottom () {
            if (this.indexListArr.length >= this.total) {
                return false;
            } else {
                this.search.pageNum++;
            }
            this.$emit('getList', this.search);
        },
        scrollTopUp (e) {
            console.log('滚动到顶部', e);
        },
        //  触摸开始
        touchStart (e, value) {
            // console.log('触摸开始');
            this.touchSatartX = e.touches[0].clientX;
            this.touchSatartY = e.touches[0].clientY;
        },
        // 触摸结束
        touchEnd (e, value) {
            if (value == '2' && this.lastScrollTop != 0) {
                return;
            }
            // console.log("触摸结束", e);
            let deltaX = e.changedTouches[0].clientX - this.touchSatartX;
            let deltaY = e.changedTouches[0].clientY - this.touchSatartY;
            // 计算移动的距离
            const distanceX = Math.abs(deltaX);
            const distanceY = Math.abs(deltaY);
            if (Math.abs(deltaY) > 50 && Math.abs(deltaX) < Math.abs(deltaY)) {
                if (deltaY < 0) {
                    // console.log('上滑');
                    this.upCount++;
                    this.touchCount = 0;
                    this.isScrollY = true;
                    // this.touchHeight = '100%';
                    this.touchHeight = `calc(100vh - ${this.navHeight}px - ${this.tabbarHeight}px + 50px)`;
                    this.$emit('update:showToatal', true);
                } else {
                    // console.log('下滑');
                    this.touchCount++;
                    this.upCount = 0;
                    // console.log('下拉');
                    if (this.touchCount > 1) {
                        this.close();
                    } else {
                        this.touchHeight = '972rpx';
                    }
                    this.isScrollY = false;
                    this.$emit('update:showToatal', false);
                }
            } else if (distanceX < this.threshold && distanceY < this.threshold) {
                // this.goPage();
            }
        },
    },

}
</script>

<style lang="scss">
.card_container {
    display: flex;
    flex-direction: column;
}
.card-content {
    height: 100%;
    z-index: 1;
    @include bgUrl('bg_half.png');
    // background-position: center 87px;
    background-size: 100% 100%;
    transition-timing-function: ease-out;
    transition-duration: 300ms;
    &.allBg {
        @include bgUrl('bg_all.png');
        background-size: 100% auto;
    }
    .content-top {
        display: grid;
        justify-items: center;
        grid-template-columns: repeat(4, 25%);
        row-gap: 20rpx;
        margin-top: 40rpx;
        margin-bottom: 43rpx;
        .top-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .top-icon {
                width: 80rpx;
                height: 80rpx;
            }
        }
    }
    .list-title {
        width: 100%;
        font-weight: bold;
        font-size: 34rpx;
        color: #1a1818;
        margin-left: 20rpx;
    }
    .scroll_parnet {
        flex: 1;
        box-sizing: border-box;
        margin-bottom: 50px;
        position: relative;
    }
    .recomm-scroll {
        width: 100%;
        // height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 20rpx;

        // margin-bottom: 120rpx;
        .content-body {
            display: grid;
            justify-items: center;
            place-items: start center;
            align-content: start;
            grid-template-columns: repeat(3, 33.33%);
            row-gap: 20rpx;
            margin-top: 30rpx;
            height: 80%;

            .body-item {
                width: 228rpx;
                height: 228rpx;
                @include bgUrl('bg_picBot.png');
                background-size: 100% 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                .item-picture {
                    width: 202rpx;
                    height: 202rpx;
                    background: #f1efea;
                    border-radius: 6rpx;
                    border: 2rpx solid #d0c9bc;
                }
                .item-tips {
                    width: 106rpx;
                    height: 32rpx;
                    background: linear-gradient(0deg, rgba(218, 186, 139, 0.8) 0%, #f7dfc1 100%);
                    border-radius: 6rpx 0px 6rpx 0px;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #1a1818;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: absolute;
                    bottom: 13rpx;
                    right: 13rpx;
                }
            }
        }
    }
}
</style>
