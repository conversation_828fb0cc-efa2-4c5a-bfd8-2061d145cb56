<template>
	<view class="custom-bottombar" :style="{ height }">
		<view class="placeholder" :style="{ height }"></view>
		<view class="fixed-bar" :style="{ height }">
			<view class="fixed-bar_inner" :class="{'align_right': alignRight}">
				<slot></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"custom-bottombar",
		props: {
			height: {
				type: String,
				default: '112rpx'
			},
			alignRight: String  // right
		}
	}
</script>

<style lang="scss" scoped>
.custom-bottombar {
	height: 112rpx;
	.fixed-bar {
		height: 112rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		width: 100%;
		position: fixed;
		bottom: 0;
		background: #FCFBF7;
		box-shadow: 3rpx 0rpx 10rpx 0rpx #F2F1F0;
		z-index: 99;
		.fixed-bar_inner {
			padding: 28rpx 30rpx 0;
			&.align_right {
				display: flex;
				justify-content: flex-end;
			}
		}
	}
	.placeholder {
		height: 100%;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
}
</style>