<template>
	<custom-page :bg-top="navHeight" :empty="list==false">
		<custom-header title="排队叫号" :navheight.sync="navHeight" show-loading></custom-header>
		<view class="party-box" v-if="list==true">
			<view class="party-item" >
				<view class="card-title">
					《梦回大唐：奇幻时空之旅》
				</view>
				<view class="sub-card">
					<view class="sub-text">
						当前号码
					</view>
					<view class="sub-text-center">
						{{number}}
					</view>
					<view class="sub-time">
						排队时间：<text class="bold-text"> {{time}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="bottom" v-if="list==true">
			<view class="button" @click="clickAfter">下一位</view>
		</view>
		<u-popup v-if="list==true" :show="show" @close="close" mode="center" :round="7" >
			<view class="popbox">
				 <view class="pop-text">当前排队号码是最后一位！</view>
				 <view class="pop-button" @click="yes">我知道了</view>
			</view>
		</u-popup>
	</custom-page>
</template>

<script>
	import {getNowNumber,useAndGetNextNumber} from "@/api/party.js"
	
	export default {
		data() {
			return {
				list:false,
				show:false,
	            navHeight:0,
				content:'当前排队号码是最后一位！',
				number:'',
				time:'',
				id:''
			};
		},
		filters:{
		
		},
		onLoad(){
		  this.getNowNumber()
		},
		onShow(){

		},
		onReachBottom(){
			
		},
		onPullDownRefresh(v){
		   this.pullDown()		
		},
		methods:{
			pullDown(){
				setTimeout(()=>{
					uni.stopPullDownRefresh()
				},400)
				this.getNowNumber()
			},
			yes(){
				this.show = false
				this.getNowNumber()
			},
			getNowNumber(){
				getNowNumber().then(res=>{
					if(res.id == null){
						this.list = false
					}else{
						this.list = true
						this.number = res.numberText
						this.time = res.createTime
						this.id = res.id
						this.$forceUpdate()
					}
					
				})
			},
			clickAfter(){
				let params = {
					id:this.id
				}
				useAndGetNextNumber(params).then(res=>{
					if(res.id == null){
						this.show = true
					}else{
						this.number = res.numberText
						this.time = res.createTime
						this.id = res.id
						this.$forceUpdate()
					}
					
				})
			},
			close(){
				this.show = false
			}
		}
	}
</script>

<style lang="scss">
::v-deep .u-popup__content {
	width: 600rpx !important;
}
::v-deep .u-safe-bottom{
	display: none;
}
.popbox{
	display: flex;
	flex-direction: column;
	.pop-text{
		width: 100%;
		height: 200rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 30rpx;
		color: #1A1818;
		border-bottom: 1rpx solid #D0C9BC;
	}
	.pop-button{
		width: 100%;
		height: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 30rpx;
		color: #1A1818;
	}
}
.party-box{
	width: calc(100% - 50rpx);
	margin-left: 25rpx;
	.party-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20rpx;
		@include bgUrl('party_list_card.png');
		background-size: 100% 100%;
		padding: 20rpx;
	
		.card-title {
			width: calc(100% - 40rpx);
			margin: 20rpx;
			height: 100rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #1A1818;
			display: flex;
			align-items: center;
			justify-content: center;
			border-bottom: 1rpx solid #D0C9BC;
		}
		.sub-card {
			box-sizing: border-box;
			margin: 10rpx 0 0 2rpx;
			width: 100%;
			@include bgUrl('party_sub_card.png');
			background-size: 100% 100%;
			height: 300rpx;
			z-index: 2;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding: 60rpx 0;
	
			.sub-text {
				font-weight: bold;
				font-size: 30rpx;
				color: #1A1818;
			}
			
			.sub-time{
				font-weight: 400;
				font-size: 26rpx;
				color: #666363;
			}
	
			.sub-text-center {
				font-weight: bold;
				font-size: 32rpx;
				color: #B36859;
				margin: 20rpx 0 30rpx 0;
			}
		}
	}
}
.bottom{
	width: 100%;
	height: 112rpx;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	background: #FCFBF7;
	box-shadow: 0rpx -3rpx 10rpx 0rpx rgba(242,241,240,0.35);
	position: fixed;
	bottom: 0;
	.button{
		margin-right: 30rpx;
		width: 150rpx;
		height: 56rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(0deg, #DABA8B 0%, #F7DFC1 100%);
		border-radius: 8rpx;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 26rpx;
		color: #1A1818;
	}
}
</style>
