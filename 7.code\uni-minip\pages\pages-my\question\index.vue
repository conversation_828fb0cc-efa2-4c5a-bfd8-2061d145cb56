<template>
	<custom-page bg="question-bg.png" :bg-top="navHeight">
		<custom-header title="常见问题" :navheight.sync="navHeight"></custom-header>
		<view class="marBottom" v-for="(item,index) in questionList">
			<view class="box">
				<view class="box-top">
					<text class="bt-l">- Q{{index+1}}</text>
					<text>{{item.q}}</text>
					<image class="bt-img" :src="imgUrls+'question-text.png'" mode=""></image>
				</view>
				<view class="box-bottom">
					<text class="bb-l">- A{{index+1}}</text>
					<text class="bb-r">{{item.a}}</text>
					<image class="bt-img" :src="imgUrls+'question-answer.png'" mode=""></image>
				</view>
				<view class="line">
					<view class="line-l"></view>
					<view class="line-r"></view>
				</view>
			</view>
		</view>
	</custom-page>
</template>

<script>
	
	import imgUrl from "@/utils/config.js"
	import data from "./data.json"
	export default {
		data() {
			return {
				imgUrls: imgUrl.IMG_URL,
				navHeight: 0,
				questionList:data
			};
		}
	}
</script>

<style lang="scss">
.marBottom{
	margin-bottom: 20rpx;
	.box{
		 width: calc(100% - 40rpx);
		 height: auto;
		 margin-left: 20rpx;
		 overflow: hidden;
		 .box-top{
			 width: 100%;
			 height: auto;
			 margin-top: 40rpx;
			 margin-bottom: 42rpx;
			 position: relative;
			 font-weight: bold;
			 font-size: 30rpx;
			 color: #1A1818;
			 font-style: normal;
			 .bt-l{
				 margin-right: 32rpx;
			 }
			 .bt-img{
				 width: 165rpx;
				 height: 30rpx;
				 position: absolute;
				 top: 14rpx;
				 left: 0;
				 z-index: -1;
			 }
			 
		 }
		 .box-bottom{
			 width: 100%;
			 height: auto;
			 margin-bottom: 40rpx;
			 position: relative;
			 .bb-l{
				font-weight: bold;
				font-size: 30rpx;
				color: #1A1818;
				font-style: normal;
				margin-right: 32rpx;
			 }
			 .bb-r{
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				font-style: normal;
			 }
			 .bt-img{
				 width: 139rpx;
				 height: 23rpx;
				 position: absolute;
				 top: 14rpx;
				 left: 0;
				 z-index: -1;
			 }
		 }
		 .line{
			 width:100%;
			 display: flex;
			 align-items: center;
			 .line-l{
				 width: 50rpx;
				 height: 5rpx;
				 background: $sec-font-color;
			 }
			 .line-r{
				 flex: 1;
				 height: 1rpx;
				 background: #D0C9BC;
			 }
		 }
	}
}
</style>
