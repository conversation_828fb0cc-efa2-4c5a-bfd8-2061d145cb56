
import { request } from '@/utils/request.js'
export const getQueue = (type)=>{
  return request({
    url: 'museum-app/vr-queue/getQueue?type='+type,
    method: 'get'
  })
}
export const cancelQueue = ()=>{
  return request({
    url: 'museum-app/vr-queue/cancelQueue',
    method: 'get'
  })
}
export const vrSave = (data) => {
    return request({
        url: 'museum-app/vr-queue/save',
        method: 'get',
        data: data
    });
};
export const getNowNumber = (data)=>{
  return request({
    url: 'museum-app/vr-queue/getNowNumber',
    method: 'get'
  })
}
export const useAndGetNextNumber = (data) => {
    return request({
        url: 'museum-app/vr-queue/useAndGetNextNumber',
        method: 'get',
        data: data
    });
};
export const getQueuePermission = ()=>{
  return request({
    url: 'museum-app/order-admission/getQueuePermission',
    method: 'get'
  })
}
export const getTemplateId = ()=>{
  return request({
    url: 'museum-app/vr-queue/getTemplateId',
    method: 'get'
  })
}
