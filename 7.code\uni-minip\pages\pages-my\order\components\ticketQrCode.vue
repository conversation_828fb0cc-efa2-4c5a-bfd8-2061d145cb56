<template>
	<view class="ticket-qr-code">
		<view class="swiper-container">
			<view :style="{opacity: current > 0 ? 1 : 0}" class="ctrl-btn left" @click="changeTicket(-1)"></view>
			<swiper class="swiper" :current="current" :duration="300" :autoplay="false" @change="onSwiperChange">
				<swiper-item v-for="(item, index) in list" :key="index">
					<view class="swiper-item">
						<image v-if="item.status === 0" :src="item.url"></image>
						<view v-else class="invalid">券码已失效</view>
					</view>
				</swiper-item>
			</swiper>
			<view :style="{opacity: current < list.length - 1 ? 1 : 0}" class="ctrl-btn right" @click="changeTicket(1)">
			</view>
		</view>
		<view class="custom-indicator"><text>{{ current + 1 }}</text>/{{ list.length }}</view>
	</view>
</template>

<script>
	export default {
		props: {
			value: Number,
			list: Array
		},
		data() {
			return {
				current: 0,
				/* list: [
					{
						url: 'https://tvax1.sinaimg.cn/large/650694a1ly1hriyowu08wj20b40b40vh.jpg',
						status: 0
					},
					{
						url: 'https://tvax1.sinaimg.cn/large/650694a1ly1hriyowu08wj20b40b40vh.jpg',
						status: 1
					},
					{
						url: 'https://tvax1.sinaimg.cn/large/650694a1ly1hriyowu08wj20b40b40vh.jpg',
						status: 0
					}
				] */
			}
		},
		methods: {
			changeTicket(num) {
				if (this.current === 0 && num === -1 || this.current === this.list.length - 1 && num === 1) return
				this.current += num
				this.$emit("input", this.current)
			},
			onSwiperChange(e) {
				this.current = e.detail.current
				this.$emit("input", this.current)
			}
		}
	}
</script>

<style scoped lang="scss">
	.ticket-qr-code {

		.swiper-container {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 50rpx;
			height: 280rpx;
			
			.swiper {
				width: 230rpx;
				height: 230rpx;
			}
			.swiper-item {
				width: 100%;
				height: 100%;
				image, .invalid {
					width: 100%;
					height: 100%;
				}
				.invalid {
					background: #F7F7F2;
					font-weight: 400;
					font-size: 24rpx;
					color: #292828;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.ctrl-btn {
				width: 70rpx;
				height: 70rpx;
				@include bgUrl("swiper-left-icon.png");
				background-size: 36rpx;
				background-position: center;
				transition: all .3s;

				&.right {
					transform: rotate(180deg);
				}
			}
		}

		.custom-indicator {
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 26rpx;
			color: #1A1818;
			height: 40rpx;

			text {
				font-weight: bold;
				font-size: 28rpx;
				color: $sec-font-color;
			}
		}
	}
</style>