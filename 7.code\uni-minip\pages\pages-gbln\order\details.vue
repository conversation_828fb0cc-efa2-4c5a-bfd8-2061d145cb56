<template>
	<custom-page :safe-area-inset-bottom="true">
		<custom-header title="订单详情" :show-back-btn="!hideHomeBtn"></custom-header>
		<view class="order-details-container">
			<view class="order-status">
				<view>{{ details.orderStatusText || orderStatusMap[details.orderStatus].title }}</view>
				<view class="sub_status">
					<view v-if="details.orderStatus === statusMap.PENDING_PAYMENT" class="count-down-inner">
						剩余<u-count-down :time="details.milliSecond" format="mm:ss"></u-count-down>自动关闭订单</view>
					<view v-else-if="refundProgressStatus.includes(details.orderStatus)" class="complete-price">
						退款金额：<text>￥{{ details.refundAmount }}</text></view>
					<view v-else>{{ orderStatusMap[details.orderStatus].subTitle }}</view>
				</view>
			</view>
			<!-- 购票信息 -->
			<view class="ticket-card">
				<custom-title bg-type="2">购票信息</custom-title>
				<view class="ticket-title">
					<image v-if="details.bannerUrl" :src="details.orderCategory === 2 ? sourceInfo.img : UPLOAD_IMG_URL + details.bannerUrl"></image>
					<view>
						<view class="hall-name">{{ details.orderCategory === 2 ? sourceInfo.hallTitle : details.exhibitionName }}</view>
						<view class="people-num">参观人数：{{ details.visitorsNum || 0 }}人</view>
					</view>
				</view>
				<view class="ticket-content">
					<view class="flex_jus_sb" v-for="item in details.orderAdmission" :key="item.id">
						<view class="name">{{ item.admissionName }} <custom-number>{{ item.buyNum }}</custom-number>
						</view>
						<view class="price">￥{{ item.admissionPrice }}</view>
					</view>
				</view>
				<view class="divider"></view>
				<view class="ticket-statistics bg-white">
					<view class="flex_jus_sb normal-cell">
						<view class="label">参观日期</view>
						<view class="value">{{ details.admissionDateStr }}</view>
					</view>
					<view class="flex_jus_sb normal-cell">
						<view class="label">订单总计</view>
						<view class="value total-price">￥ {{ details.totalAmount || 0 }}</view>
					</view>
				</view>
			</view>
			<!-- 门票信息 -->
			<view v-if="showQrCode" class="qrcode-card mt20">
				<custom-title bg-type="2">门票信息</custom-title>
				<view class="content bg-white">
					<ticket-qr-code v-model="currentQrCodeValue" :list="ticketQrCodeList"></ticket-qr-code>
				</view>
				<view class="divider"></view>
				<view class="bg-white">
					<view class="flex_jus_sb normal-cell">
						<view class="label">参观人</view>
						<view class="value">{{ details.orderAdmissionDetail[currentQrCodeValue].contactsName }}</view>
					</view>
					<view class="flex_jus_sb normal-cell">
						<view class="label">券码状态</view>
						<view class="value">{{ details.orderAdmissionDetail[currentQrCodeValue].statusText }}</view>
					</view>
					<view class="flex_jus_sb normal-cell">
						<view class="label">核销时间</view>
						<view class="value">
							{{ details.orderAdmissionDetail[currentQrCodeValue].verifincationTime || '-'}}
						</view>
					</view>
				</view>
			</view>
			<!-- 游客信息 -->
			<view class="visitors-card mt20">
				<custom-title bg-type="2">游客信息</custom-title>
				<view class="bg-white">
					<view class="visitors-item" v-for="(item, index) in visitorsListParse" :key="index">
						<view class="flex_jus_sb normal-cell">
							<view class="label">参观人</view>
							<view class="value">{{ item.contactsName }}</view>
						</view>
						<view class="flex_jus_sb normal-cell normal-cell_dot">
							<view class="label">手机号</view>
							<view class="value">{{ encryptPhone(item.contactsPhone) }}</view>
						</view>
						<view class="flex_jus_sb normal-cell normal-cell_dot">
							<view class="label">身份证号</view>
							<view class="value">{{ encryptIdCard(item.contactsCredentialNo) }}</view>
						</view>
					</view>
				</view>
				<template v-if="details.orderAdmissionDetail.length > 1">
					<view class="divider"></view>
					<view class="more-visitors tap-style" @click='showMoreVisitorsStatus = !showMoreVisitorsStatus'>
						{{ showMoreVisitorsStatus ? '收起' : '展开' }}全部
					</view>
				</template>
				<u-gap v-else height="10rpx" bg-color="#fff"></u-gap>
			</view>
			<!-- 订单信息 -->
			<view class="order-card mt20">
				<custom-title bg-type="2">订单信息</custom-title>
				<view class="bg-white">
					<view class="flex_jus_sb normal-cell">
						<view class="label">订单编号</view>
						<view class="value">{{ details.orderSn }}</view>
					</view>
					<view class="flex_jus_sb normal-cell">
						<view class="label">下单时间</view>
						<view class="value">{{ details.addTime }}</view>
					</view>
					<template v-if="details.orderStatus !== statusMap.PENDING_PAYMENT">
						<view v-if="payTimeAndTradeTime.includes(details.orderStatus)" class="flex_jus_sb normal-cell">
							<view class="label">支付时间</view>
							<view class="value">{{ details.payTime }}</view>
						</view>
						<view v-if="payTimeAndTradeTime.includes(details.orderStatus)&&details.tradeSn"
							class="flex_jus_sb normal-cell">
							<view class="label">交易单号</view>
							<view class="value">{{ details.tradeSn }}</view>
						</view>
						<view v-if="details.orderStatus === statusMap.COMPLETED&&details.confirmTime"
							class="flex_jus_sb normal-cell">
							<view class="label">订单完成时间</view>
							<view class="value">{{ details.confirmTime }}</view>
						</view>
						<view v-if="refundTime.includes(details.orderStatus)" class="flex_jus_sb normal-cell">
							<view class="label">退款申请时间</view>
							<view class="value">{{ details.refundApplyTime }}</view>
						</view>
						<view v-if="details.orderStatus === statusMap.REFUNDED" class="flex_jus_sb normal-cell">
							<view class="label">退款完成时间</view>
							<view class="value">{{ details.refundCompleteTime }}</view>
						</view>
						<view v-if="details.orderStatus === statusMap.CLOSED" class="flex_jus_sb normal-cell">
							<view class="label">取消订单时间</view>
							<view class="value">{{ details.closeTime }}</view>
						</view>
					</template>
				</view>
			</view>

		</view>

		<custom-bottombar>
			<view class="flex_jus_sb">
				<view class="bottom-bar-price">
					<templete v-if="details.orderStatus === statusMap.PENDING_PAYMENT">
						总计 <text>￥ {{ details.totalAmount || '0.00' }}</text>
					</templete>
				</view>
				<view class="buttons">
					<custom-button @click="goHome" type="info">返回首页</custom-button>
					<custom-button v-if="details.orderStatus === statusMap.PENDING_PAYMENT" type="primary"
						@click="toPay">去支付</custom-button>
					<custom-button v-if="details.canRefund" type="primary"
						@click="toPage(`/pages/pages-my/order/requestRefund?orderSn=${orderSn}`)">申请退款</custom-button>
					<custom-button v-if="details.canDelete" type="info"
						@click="delModalStatus = true">删除订单</custom-button>
					<custom-button v-if="refundProgressStatus.includes(details.orderStatus)" type="primary"
						@click="toPage(`/pages/pages-my/order/requestRefund?orderSn=${orderSn}&isView=true`)">退款进度</custom-button>
				</view>
			</view>
		</custom-bottombar>

		<custom-modal :visible.sync="delModalStatus" title="确定要删除此订单吗？" @confirm="onDelOrder"></custom-modal>
	</custom-page>
</template>

<script>
	import TicketQrCode from './components/ticketQrCode.vue';
	import {
		getOrderDetail,
		delOrder,
		getTmplIds,
	} from '@/api/order';
	import statusMap from './js/orderStatusMap';
	import {
		encryptIdCard,
		encryptPhone,
		payment
	} from "@/utils/index.js"
	import {
		UPLOAD_IMG_URL
	} from '@/utils/config';
	import {
		log
	} from 'util';

	export default {
		components: {
			TicketQrCode
		},
		data() {
			return {
				tmplIds: '',
				pageType: null,
				UPLOAD_IMG_URL,
				encryptIdCard,
				encryptPhone,
				statusMap,
				visitorsList: 1,
				showMoreVisitorsStatus: false,
				orderSn: null,
				orderStatusMap: {
					[statusMap.PENDING_PAYMENT]: { // 1
						title: '待付款'
					},
					[statusMap.CLOSED]: { // 2
						title: '已关闭',
						subTitle: '超时未支付，交易关闭'
					},
					[statusMap.TO_BE_USED]: { // 3
						title: '待使用',
						subTitle: '已核销与部分核销的订单不支持退款'
					},
					[statusMap.COMPLETED]: { // 4
						title: '已完成'
					},
					[statusMap.REFUNDING]: { // 5
						title: '退款中'
					},
					[statusMap.REFUNDED]: { // 6
						title: '已退款'
					},
					[statusMap.REFUND_FAILED]: { // 7
						title: '退款失败'
					},
					[statusMap.EXPIRED]: { // 400
						title: '过期未使用'
					}
				},
				// 门票信息二维码模块
				ticketQrCodeRules: [statusMap.TO_BE_USED, statusMap.COMPLETED],
				// 支付时间
				payTimeAndTradeTime: [statusMap.TO_BE_USED, statusMap.COMPLETED, statusMap.REFUNDING, statusMap.REFUNDED,
					statusMap.REFUND_FAILED
				],
				// 退款申请时间
				refundTime: [statusMap.REFUNDING, statusMap.REFUNDED, statusMap.REFUND_FAILED],
				// 删除订单按钮
				// delOrderStatus: [statusMap.CLOSED, statusMap.REFUNDED, statusMap.REFUND_FAILED],
				// 退款进度
				refundProgressStatus: [statusMap.REFUNDING, statusMap.REFUNDED, statusMap.REFUND_FAILED],
				details: {
					// orderStatus: 200,
					bannerUrl: '',
					orderAdmission: [],
					orderAdmissionDetail: []
				},
				currentQrCodeValue: 0,
				delModalStatus: false,
				screenBrightness: 1,
				hideHomeBtn: false // 隐藏首页按钮

			};
		},
		computed: {
			visitorsListParse() {
				return this.details.orderAdmissionDetail.slice(0, this.showMoreVisitorsStatus ? this.details
					.orderAdmissionDetail.length : 1)
			},
			showQrCode() {
				return this.ticketQrCodeRules.includes(this.details.orderStatus)
			},
			ticketQrCodeList() {
				return this.details.orderAdmissionDetail.map(item => {
					return {
						url: UPLOAD_IMG_URL + item.verifincationPicUrl,
						status: item.verifincationStatus
					}
				})
			},
			sourceInfo() {
				return this.$store.state.sourceInfo || {};
			}
		},
		onLoad(options) {
			this.pageType = options.pageType
			this.orderSn = options.orderSn
			this.hideHomeBtn = !!options.hideHomeBtn
			/* if (this.pageType == 1) {
				this.showTips()
			} */
			console.log(this.pageType, '2222222')
			// this.init()
		},
		onShow() {
			this.setScreenBrightness(1)
			this.init()
		},
		methods: {
			setScreenBrightness(value) {
				uni.getScreenBrightness({
					success: res => {
						console.log(res)
						this.screenBrightness = res.value
					},
					complete: () => {
						uni.setScreenBrightness({
							value,
							complete: (res) => {
								console.log(res)
							}
						})
					}
				})
			},
			async init() {
				const data = await getOrderDetail({
					orderSn: this.orderSn
				})
				console.log("订单详情", data)
				this.details = data
				if (data.orderStatus === statusMap.CLOSED) {
					this.orderStatusMap[statusMap.CLOSED].subTitle = data.closeType === 1 ? '已取消订单' : '超时未支付，交易关闭'
				}
			},
			async getTmp() {

			},
			//订阅消息弹框
			async showTips() {
				let that = this
				const data = await getTmplIds({})
				this.tmplIds = data
				uni.showModal({
					title: '温馨提示',
					content: '游览入场提醒',
					confirmText: "同意",
					cancelText: "拒绝",
					success: function(res) {
						if (res.confirm) {
							uni.requestSubscribeMessage({
								tmplIds: that.tmplIds,
								success: (res) => {
									// console.log(res, '9999999');
								},
								fail(err) {
									console.log(err);
								}
							})
						} else if (res.cancel) {
							///显示第二个弹说明一下
							wx.showModal({
								title: '温馨提示',
								content: '拒绝后您将无法收到入场提醒消息',
								confirmText: "知道了",
								showCancel: false,
								success: function(res) {}
							});
						}
					}

				})
			},
			async toPay() {
				await payment(this.orderSn)
				await this.init()
			},
			async onDelOrder() {
				const {
					code,
					msg
				} = await delOrder({
					orderSn: this.orderSn
				})
				if (code === 200) {
					uni.showToast({
						title: msg || '删除成功',
						icon: 'none'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				}
			},
			toPage(url) {
				uni.navigateTo({
					url
				})
			},
			goHome() {
				if (this.hideHomeBtn) {
					uni.reLaunch({
						url: '/pages/pages-gbln/ticketBooking/ticketBooking'
					})
				} else {
					uni.navigateBack({
						delta: 999
					})
				}
			}
		},
		onHide() {
			this.setScreenBrightness(this.screenBrightness)
		},
		onUnload() {
			this.setScreenBrightness(this.screenBrightness)
		}
	}
</script>

<style lang="scss" scoped>
	.radius {
		border-radius: 6rpx;
		overflow: hidden
	}

	.bg-white {
		background-color: #fff;
		padding: 0 20rpx;
	}

	.order-details-container {
		@include bgUrl("order_detail_bg.png");
		padding: 0 20rpx;

		.order-status {
			color: #fff;
			font-weight: bold;
			font-size: 34rpx;
			height: 150rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.sub_status_common {
				font-weight: 400;
				font-size: 24rpx;
				color: #F7F7F2;
			}

			.sub_status {
				@extend .sub_status_common;
				height: 40rpx;
				line-height: 40rpx;

				.count-down-inner {
					display: flex;
					align-items: center;

					::v-deep .u-count-down__text {
						@extend .sub_status_common;
					}
				}

				.complete-price {
					text {
						font-weight: bold;
						font-size: 30rpx;
						color: #F7F7F2;
					}
				}
			}
		}

		.normal-cell {
			height: 50rpx;
			padding-top: 10rpx;

			.label {
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
			}

			.value {
				font-weight: 400;
				font-size: 26rpx;

				&.total-price {
					font-weight: bold;
					font-size: 32rpx;
					color: $sec-font-color;
				}
			}

			&_dot {
				.label {
					padding-left: 20rpx;
					position: relative;

					&:before {
						display: block;
						content: '';
						width: 6rpx;
						height: 6rpx;
						background: #D1C9BA;
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						border-radius: 50%;
					}
				}
			}
		}

		.ticket-card {
			@extend .radius;

			.ticket-title {
				display: flex;
				gap: 17rpx;
				background-color: #fff;
				padding: 14rpx 20rpx;

				image {
					width: 162rpx;
					height: 92rpx;
					border-radius: 6rpx;
				}

				>view {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					padding: 4rpx 0;
				}

				.hall-name {
					font-weight: bold;
					font-size: 28rpx;
				}

				.people-num {
					font-weight: 400;
					font-size: 24rpx;
					color: $tip-font-color;
				}
			}

			.ticket-content {
				background-color: #fff;
				padding: 10rpx 20rpx 0;

				>view {
					min-height: 70rpx;
					font-weight: 400;
					font-size: 28rpx;
					gap: 60rpx;

					.name {
						display: flex;
						align-items: center;
						gap: 13rpx;
					}

					.price {
						font-weight: bold;
						font-size: 28rpx;
					}
				}
			}

			.ticket-statistics {
				padding-bottom: 30rpx;
			}
		}

		.visitors-card {
			@extend .radius;

			.visitors-item {
				&+.visitors-item {
					margin-top: 14rpx;
					border-top: 2rpx dashed #d0c9bc;
				}
			}

			.more-visitors {
				height: 70rpx;
				background: #FFFFFF;
				font-weight: 400;
				font-size: 28rpx;
				text-align: center;
				line-height: 70rpx;
			}
		}

		.order-card {
			@extend .radius;

			.bg-white {
				padding-bottom: 30rpx;
			}
		}

		.qrcode-card {
			@extend .radius;

			.bg-white {
				padding-bottom: 30rpx;
			}
		}

	}

	.buttons {
		display: flex;
		gap: 30rpx;
	}
</style>