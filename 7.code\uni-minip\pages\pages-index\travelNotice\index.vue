<template>
	<custom-page bg="notice-bg.png">
		<custom-header title="游玩须知" :navheight.sync="navHeight"></custom-header>
		<view class="startTime">
			<view class="top">
				<view class="top-img top-imgOne"></view>
				<view class="top-text">开放时间</view>
			</view>
			<view class="content">
				<view class="openlist" v-for="(item,index) in openlist">
					<view class="data">{{item.startTime.substring(0,10)}}至{{item.endTime.substring(0,10)}}</view>
					<view class="specify">{{item.specify}}</view>
				</view>
			</view>
		</view>
		<view class="titles">
			<view class="icon">*</view>
			<view class="text">以上信息仅供参考，具体以展厅当日实际公示信息为准</view>
		</view>
		<view class="startTime" style="margin-top: 0 !important;">
			<view class="top">
				<view class="top-img top-imgTwo"></view>
				<view class="top-text">优待政策</view>
			</view>
			<text class="content padding30">{{ info.preferentialPolicies }}</text>
		</view>
		<view class="startTime">
			<view class="top">
				<view class="top-img top-imgThree"></view>
				<view class="top-text">交通信息</view>
			</view>
			<text class="content padding30">{{ info.trafficInformation }}</text>
		</view>
		<view class="startTime">
			<view class="top">
				<view class="top-img top-imgFour"></view>
				<view class="top-text">停车场</view>
			</view>
			<text class="content padding30">{{ info.parkingLot }}</text>
		</view>
		<view class="startTime">
			<view class="top">
				<view class="top-img top-imgFive"></view>
				<view class="top-text">温馨提示</view>
			</view>
			<text class="content padding30">{{ info.tips }}</text>
		</view>
		<view class="startTime">
			<view class="top">
				<view class="top-img top-imgSix"></view>
				<view class="top-text">咨询热线</view>
			</view>
			<view class="content padding30" @click="onTel">
				{{info.tel}}
			</view>
		</view>
	</custom-page>
</template>
<script>
	import { travelNotice } from "@/api/home.js"
	export default {
		data() {
			return {
				navHeight:0,
				openlist:[],
				info:{}
			};
		},
		onLoad(){
			this.getTravelNotice();
		},
		methods:{
			getTravelNotice(){
				travelNotice().then((res)=>{
					this.openlist = res.openDescriptionList
					this.info = res
				})
			},
			onTel() {
				uni.makePhoneCall({
					phoneNumber: this.info.tel,
					complete: res => {
						// console.log(res)
					}
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
.titles{
	display: flex;
	align-items: center;
    width: calc(100% - 60rpx);
	height: 30rpx;
	line-height: 30rpx;
	margin:  30rpx 30rpx 38rpx 30rpx;
	overflow: hidden;
	.icon{
		width: 13rpx;
		height: 12rpx;
		line-height: 12rpx;
		font-weight: bold;
		font-size: 28rpx;
		color: $sec-font-color;
		margin-right: 10rpx;
		margin-top: 10rpx;
	}
	.text{
		font-weight: 400;
		font-size: 26rpx;
		color: $tip-font-color;
	}
}
.startTime{
	width: calc(100% - 40rpx);
	margin-top: 20rpx;
	margin-left: 20rpx;
	background: linear-gradient(0deg, #FFFFFF 0%, #FAF8F2 100%);
	border-radius: 6rpx 6rpx 6rpx 6rpx;
	border: 2rpx solid #FFFFFF;
	box-sizing: border-box;
	overflow: hidden;
	.padding30{
		padding: 30rpx;
	}
	.content{
		display: block;
		background: #FAFAF8;
		border-radius: 6rpx 6rpx 6rpx 6rpx;
		border: 4rpx solid #FFFFFF;
        box-sizing: border-box;
		padding-bottom: 20rpx;
		.openlist{
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			.data{
				font-weight: bold;
				font-size: 28rpx;
				color: $main-font-color;
				padding: 16rpx 0;
			}
			.specify{
				font-weight: 400;
				font-size: 28rpx;
				color: $tip-font-color;
			}
		}
	}
	.top{
		width: 100%;
		display: flex;
		align-items: center;
		height: 40rpx;
		line-height: 40rpx;
		padding: 30rpx 20rpx;
		background: linear-gradient(0deg, #FFFFFF 0%, #FAF8F2 100%);
		.top-img{
			width: 40rpx;
			height: 40rpx;
			margin-right: 10rpx;
		}
		.top-imgOne{
			@include bgUrl('notice-openTime.png');
			background-size: 100% 100%;
		}
		.top-imgTwo{
			@include bgUrl('notice-policy.png');
			background-size: 100% 100%;
		}
		.top-imgThree{
			@include bgUrl('notice-traffic.png');
			background-size: 100% 100%;
		}
		.top-imgFour{
			@include bgUrl('notice-parkingLot.png');
			background-size: 100% 100%;
		}
		.top-imgFive{
			@include bgUrl('notice-tips.png');
			background-size: 100% 100%;
		}
		.top-imgSix{
			@include bgUrl('notice-hotline.png');
			background-size: 100% 100%;
		}
		.top-text{
			height: 40rpx;
			line-height: 40rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
}
</style>


