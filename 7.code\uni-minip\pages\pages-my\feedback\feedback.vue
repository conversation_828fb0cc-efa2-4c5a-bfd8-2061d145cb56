<template>
	<custom-page :bg-top="navHeight">
		<custom-header title="意见反馈" :navheight.sync="navHeight"></custom-header>
		<view class="topLine"></view>
		<view class="box">
			<view class="content">
				<view class="box-type">
					<view class="boxType-l">
						<text class="boxType-text">*</text>
						<text class="boxType-texts">意见类型</text>
					</view>
					<view class="boxType-r" @click="select">
						<view class="boxTypeR-text" :class="label=='建议'?'text-one':label=='投诉'?'text-two':''">{{label}}</view>
						<view class="boxTypeR-img"></view>		    
					</view>
				</view>
				<view class="box-describe">
					<view class="describe-l">*</view>
					<view class="describe-r">内容描述</view>
				</view>
				<view class="box-textarea">
					<view class="textarea">
						<u-textarea  v-model="info.content" placeholder="请输入内容描述" count  :maxlength="maxlength" ref="textarea"></u-textarea>
					</view>
				</view>	
				<view class="box-upload">
					<text>图片上传</text>
				</view>
				<view class="box-imglist">
					<u-upload
						:fileList="files"
						@afterRead="afterRead"
						@delete="deletePic"		
						name="1"
						multiple
						:maxCount="5"
						width="120rpx" 
						height="120rpx"
					>
					</u-upload>
				</view>
				<view class="box-imgtext">
					最多允许上传5张照片，支持jpg、png、bmp等图片格式， 图片小于5M
				</view>
				<view class="box-name">
					<view class="name">姓名</view>
					<input class="input" v-model="info.name" type="text" placeholder="请填写中文" @input="onKeyYHKNameInput" placeholder-class="p-name" :maxlength="10"/>
				</view>
				<view class="box-num">
					<view class="num">
						<view class="num-l">*</view>
						<view class="num-r">联系方式</view>
					</view>
					<input class="input" v-model="info.phone" type="number" placeholder="请填写" placeholder-class="p-name" :maxlength="11"/>
				</view>
			</view>			
		</view>
		<view class="bottomLine"></view>
		<view class="menu">
			<custom-button @tap="submit">提交</custom-button>
		</view>
		<picker ref="pic" :picklist="picklist"  @echo="echo"></picker>
	</custom-page>
</template>

<script>
	import { BASE_URL } from '@/utils/config';
	import {feedbackAdd} from "@/api/my.js"
	import picker from '@/components/custom-picker/picker.vue'
	import config from '@/utils/config.js'
	export default {
		components:{
			picker
		},
		data() {
			return {
				info:{
					name: "", //姓名
					phone: "", //手机号
					type: "", //类型 1建议 2投诉
					content: "", //意见反馈内容
					files: [] //图片地址
				},
	            navHeight:0,
				files:[],
				label:'请选择',
				value:'',
				picklist:[
					{
						label:'建议',
						value:'1'
					},
					{
						label:'投诉',
						value:'2'
					}
				],
				maxlength:200,
				disable:false, //防止重复点击
				disable2:false //防止图片上传中点击
			};
		},
		onReady() {
		   this.$refs.textarea.setFormatter((value) => {
		   	if (value.length >= 200) {
		   	  return value.substr(0, 200)
		   	}else {
		   	  return value
		   	}
		   })
		},
		methods:{
			onKeyYHKNameInput: function(event){
				this._timer = setTimeout(()=>{
						clearTimeout(this._timer)
						var value = event.target.value;
						//匹配数字的正则表达式
						var patt = /.*[0-9,a-z,A-Z]{1,}.*/i;
						//匹配特殊字符的正则表达式
						var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]");
						let _str=""
						for(let i=0,len=value.length;i<len;i++){
							_str+=value[i].replace(pattern,'').replace(patt,'')
						}
						this.info.name = _str;
					},3)
			},
			select(){
				this.$refs.pic.shows();
			},
			echo(v){
				this.label = v.target.__args__[0].label
				this.info.type =  v.target.__args__[0].value
			},
			submit(){
				if(this.disable==true){
					uni.$u.toast('请勿重复点击...')
					//防止重复点击
				}else if(this.disable==false){
					if(this.disable2==true){
						uni.$u.toast('图片上传中...')
					}else if(this.disable2==false){
						this.disable = true
						this.info.files = this.files.map((i,e)=>{
							return i.url
						}) 
						feedbackAdd(this.info).then((res)=>{
							if(res.code == 200){
								uni.$u.toast('提交成功')
								setTimeout(()=>{
									uni.redirectTo({
										url: '/pages/pages-my/feedback/index'
									});
								},400)
							}
						}).catch(
							this.disable = false
						)
					}
				}
			},
			// 删除图片
			deletePic(event) {
				this.files.splice(event.index, 1)
			},
			async afterRead(event) {
				this.disable2 = true
				let lists = [].concat(event.file)
				let fileListLen = this.files.length
				lists.map((item) => {
					this.files.push({
						...item,
						status: 'uploading',
						message: '上传中',
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this.files[fileListLen]
					this.files.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					console.log(this.files,'this.files');
					fileListLen++
				}
				
				this.disable2 = false
			},
			//上传图片
			uploadFilePromise(url) {
				let that = this
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: BASE_URL.development + 'museum-main/oss/app/upload',
						filePath: url,
						name: 'file',
						header: {
						    'Authorization': 'Bearer ' + uni.getStorageSync("token")
						},
						success: (res) => {
							let data = JSON.parse(res.data);
							console.log(data,'data');
							setTimeout(() => {
								resolve(data.data);
							}, 1000);
						}
					});
				});
			},
		},
		onLoad(){
			
		},
		onShow(){
			
		}
	}
</script>

<style lang="scss">
.topLine{
	width: calc(100% - 20rpx);
	height: 26rpx;
	margin-left: 10rpx;
	@include bgUrl('feedback-top.png');
	background-size: 100% 100% !important;
}
.bottomLine{
	width: calc(100% - 20rpx);
	height: 26rpx;
	margin-top: -4rpx;
	margin-left: 10rpx;
	@include bgUrl('feedback-bottom.png');
	background-size: 100% 100% !important;
}
//底部按钮
.menu{
  width: 100%;
  text-align: center;
  margin-top: 30rpx;
}
.box{
	width: calc(100% - 50rpx);
	margin-top: -4rpx;
	margin-left: 25rpx;
	background: #E8E2D4;
	overflow: hidden;
	.content{
		margin: 10rpx;
		background: #FFFFFF;
		overflow: hidden;
		.box-type{
			width: calc(100% - 40rpx);
			height: 100rpx;
			margin-left: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #D0C9BC;
			box-sizing: border-box;
			.boxType-l{
				height: 26rpx;
				line-height: 26rpx;
				.boxType-text{
					width: 12rpx;
					height: 12rpx;
					margin-right: 10rpx;
					color: $sec-font-color;
					font-weight: 400;
				}
				.boxType-texts{
					height: 26rpx;
					line-height: 26rpx;
					font-size: 28rpx;
					font-weight: 400;
				}
			}
			.boxType-r{
				height: 50rpx;
				line-height: 50rpx;
				display: flex;
				align-items: center;
				.boxTypeR-text{
					height: 30rpx;
					line-height: 30rpx;
					color: $tip-font-color;
					font-weight: 400;
					margin-right: 10rpx;
					font-size: 28rpx;
				}
				.text-one{
					padding:6rpx 18rpx;
					color:#ffffff;
					background:#7B9D7C;
					border-radius: 2rpx 2rpx 2rpx 2rpx;
					font-family: SourceHanSerifCN, SourceHanSerifCN;
					font-weight: 400;
					font-size: 22rpx;
				}
				.text-two{
					border-radius: 2rpx 2rpx 2rpx 2rpx;
					background:#B36859;
					color:#ffffff;
					padding:6rpx 18rpx;
					font-family: SourceHanSerifCN, SourceHanSerifCN;
					font-weight: 400;
					font-size: 22rpx;
				}
				.boxTypeR-img{
					width: 50rpx;
					height: 50rpx;
					line-height: 50rpx;
					@include bgUrl('feedback-return.png');
					background-size: 100% 100% !important;
				}
			}
		}
		.box-describe{
			width: calc(100% - 40rpx);
			height: 100rpx;
			line-height: 100rpx;
			margin-left: 20rpx;
			display: flex;
			align-items: center;
			.describe-l{
				width: 12rpx;
				height: 12rpx;
				color: $sec-font-color;
				line-height: 12rpx;
				margin-right: 10rpx;
			}
			.describe-r{
				height: 26rpx;
				line-height: 26rpx;
				font-size: 28rpx;
				font-weight: 400;
			}
		}
		.box-textarea{
			width: calc(100% - 40rpx);
			height: 280rpx;
			margin-left: 20rpx;
			margin-bottom: 20rpx;
			background: #FAF8F5;
			overflow: hidden;
			.textarea{
				width: calc(100% - 20rpx);
				height: calc(100% - 20rpx);
				margin: 10rpx;
			}
		}
		.box-upload{
			width: calc(100% - 80rpx);
			height: 100rpx;
			margin-left: 40rpx;
			display: flex;
			align-items: center;
			text{
				height: 30rpx;
				line-height: 30rpx;
				font-size: 28rpx;
				font-weight: 400;
			}
			
		}
		.box-imglist{
			width: calc(100% - 40rpx);
			height: 120rpx;
			margin-left: 20rpx;
			margin-bottom: 32rpx;
		}
		.box-imgtext{
			width: calc(100% - 40rpx);
			font-size: 24rpx;
			color: $tip-font-color;
			font-weight: 400;
			margin-left: 20rpx;
			margin-bottom: 32rpx;
		}
		.box-name{
			width: calc(100% - 40rpx);
			height: 100rpx;
			margin-left: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-top: 1rpx solid #D0C9BC;
			border-bottom: 1rpx solid #D0C9BC;
			box-sizing: border-box;
			.name{
				margin-left: 20rpx;
				font-size: 28rpx;
				font-weight: 400;
			}
			.input{
				text-align: right;
			}
		}
		.box-num{
			width: calc(100% - 40rpx);
			height: 100rpx;
			margin-left: 20rpx;
			border-bottom: 1rpx solid #D0C9BC;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			.num{
				width: 200rpx;
				height: 30rpx;
				line-height: 30rpx;
				display: flex;
				align-items: center;
				.num-l{
					font-weight: bold;
					font-size: 28rpx;
					color: $sec-font-color;
					margin-right: 12rpx;
				}
				.num-r{
					width: auto;
					font-weight: 400;
					font-size: 28rpx;
					color: #1A1818;
				}
			}
			.input{
				text-align: right;
			}
		}
	}	
	
}
//多行文本
::v-deep .u-textarea{
	height: 260rpx !important;
	padding: 0 !important;
	background-color: #FAF8F5 !important;
	border: none !important;
}
::v-deep .u-textarea__field{
	height: 220rpx !important;
}
::v-deep .u-textarea__count{
	background-color: transparent !important;
}
.p-name{
	font-family: 'songti' !important;
	font-weight: 400;
	font-size: 28rpx;
	color: #666363;
}
//上传
::v-deep .u-upload{
	height: 120rpx !important;
}
::v-deep .u-upload__wrap{
	height: 120rpx !important;
	overflow-x: auto !important; 
	flex-direction:none !important;
}
::v-deep .u-upload__wrap__preview{
	margin: 0 8rpx 0 0 !important;
}
</style>
