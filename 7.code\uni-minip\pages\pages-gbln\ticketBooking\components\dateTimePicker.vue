<template>
	<view class="date-time-picker">
		<view class="date-card-container" :class="{'date-card-container_full-screen': fullScreen}">
			<view class="date-time-item" :style="{width: itemWidth}"
				:class="{active: item.date === value.date, disabled: disabled || item.disabled, 'full-screen': fullScreen, 'no-title': !item.title}"
				v-for="(item, index) in parsedDate" :key="index" @click="onDateClick(item)">
				<view class="title">{{ item.title }}</view>
				<view class="label">{{ item.label }}</view>
				<view v-show="tag && item.date !== value.date" class="can-tag">{{ item.readonly ? readonlyTag : tag }}</view>
				<view v-show="item.date === value.date" class="select-icon"></view>
			</view>
			<view class="date-time-item more-item tap-style" :style="{width: itemWidth}"
				:class="{'full-screen': fullScreen}" v-if="data.length > 0 && showMore" @click="onCalendarOpen">
				<view class="title">更多</view>
				<view class="label"></view>
			</view>
		</view>

		<u-calendar ref="myCalender" :show="showCalendar" :default-date="defaultDate" color="#dec292" :custom-list="data" monthNum
			="2" :maxDate="lastDate" :formatter="formatter" @close="onCalendarClose" @confirm="onCalendarConfirm">
			<view slot="footer" class="calendar-footer">
				<custom-button type="primary" block @click="customConfirm">确定</custom-button>
			</view>
		</u-calendar>
	</view>
</template>

<script>
	
	export default {
		props: {
			value: {
				type: Object
			},
			data: {
				type: Array
			},
			showMore: Boolean,
			fullScreen: Boolean,
			count: { // -1为不裁剪数据
				type: Number,
				default: 3
			},
			tag: {
				type: String
			},
			readonlyTag: {
				type: String
			},
			itemWidth: {
				type: String,
				default: 'auto'
			},
			formatter: Function,
			disabled: Boolean
		},
		data() {
			return {
				showCalendar: false,
				firstDateIndex: 0,
				// defaultDate: null
			}
		},
		computed: {
			parsedDate() {
				return this.count > -1 ? this.data.slice(this.firstDateIndex, this.firstDateIndex + this.count) : this.data.slice(this.firstDateIndex);
			},
			lastDate() {
				return this.data[this.data.length - 1]?.date
			},
			defaultDate() {
				return this.data[0] ? (this.data[0]?.disabled || this.data[0]?.readonly) ? null : new Date(this.data[0]?.date) : null
			}
		},
		methods: {
			onDateClick(item) {
				if (item.disabled || item.readonly || this.disabled) return
				this.$emit('input', item)
			},
			onCalendarOpen() {
				this.showCalendar = true
			},
			onCalendarClose() {
				this.showCalendar = false
			},
			onCalendarConfirm([date]) {
				if (!date) {
					this.showCalendar = false
					return
				}
				this.firstDateIndex = this.data.findIndex(f => f.date === date)
				this.$emit('input', this.data[this.firstDateIndex])
				this.showCalendar = false
			},
			customConfirm() {
				this.$refs.myCalender.confirm()
			}
		},
		options: {
			styleIsolation: "shared"
		}
	}
</script>

<style scoped lang="scss">
	.date-time-picker {
		.date-card-container {
			padding: 0 20rpx;
			overflow-x: auto;
			display: flex;
			gap: 20rpx;
			&_full-screen {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
			}
		}
		
		::v-deep {
			.u-calendar-month__days__day__select__info:not(.u-calendar-month__days__day__select__info--disabled), .u-calendar-month__days__day__select__buttom-info:not(.u-calendar-month__days__day__select__buttom-info--disabled) {
				color: #000!important;
			}
			.u-calendar-month-wrapper {
				margin-top: 0;
				padding-top: 4rpx;
			}
		}
		.calendar-footer {
			padding: 10rpx 20rpx 0;
		}
	}
	.date-time-item {
		flex: 0 0 auto; /* 不缩放，不放大，宽度自动 */
		min-width: 152rpx;
		// width: 152rpx;
		height: 128rpx;
		text-align: center;
		background: #F7F7F2;
		border-radius: 6rpx;
		border: 2px solid #D0C9BC;
		font-weight: 400;
		font-size: 28rpx;
		line-height: 1;
		position: relative;
		box-sizing: border-box;
		// padding: 0 22rpx;
		text-align: center;
		white-space: nowrap;
		display: inline-block;
		.title {
			padding: 18rpx 0 13rpx;
		}
		.can-tag {
			position: absolute;
			right: 0;
			bottom: 0;
			width: 98rpx;
			height: 32rpx;
			background: #EBE9E2;
			border-radius: 6rpx 0rpx 0 0rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: $sec-font-color;
			line-height: 32rpx;
			text-align: center;
		}
		&.no-title {
			display: flex;
			flex-direction: column;
			justify-content: center;
			.title {
				display: none;
			}
		}
		&.active {
			background: linear-gradient(0deg, #EDDDB7 0%, #F6EDD7 100%);
			border-color: #DEC292;
			font-weight: bold;
			font-size: 28rpx;
			color: initial;
		}
		&.disabled {
			opacity: .5;
			.can-tag {
				display: none;
			}
		}
		&.more-item {
			.title {
				padding-bottom: 3rpx;
			}
			.label {
				width: 50rpx;
				height: 50rpx;
				@include bgUrl("<EMAIL>");
				margin: 0 auto;
			}
		}
		.select-icon {
			width: 32rpx;
			height: 32rpx;
			position: absolute;
			right: 0;
			bottom: 0;
			@include bgUrl("icon_check.png");
		}
	}
</style>