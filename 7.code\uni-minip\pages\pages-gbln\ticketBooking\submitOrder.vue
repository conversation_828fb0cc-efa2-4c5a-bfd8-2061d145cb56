<template>
    <custom-page bg="ticket_booking_bg.png">
        <custom-header title="提交订单" :scroll-top="scrollTop" :scroll-ratio="2"></custom-header>
        <view class="submit-order-container">
            <custom-title bg-type="2">
                购票信息
                <custom-button type="info" slot="right" @click="goBack">修改</custom-button>
            </custom-title>
            <view class="bg-white">
                <view class="exhibition-hall-name">{{ sourceInfo.hallTitle }}</view>
                <view class="ticket-item flex_jus_sb" v-for="item in details.admissionList" :key="item.id">
                    <view>{{ item.admissionName }} <custom-number>{{ item.buyNum }}</custom-number></view>
                    <view class="price">￥{{ item.price }}</view>
                </view>
            </view>
            <view class="divider"></view>
            <view class="bg-white visit-date flex_jus_sb mb20">
                <view>参观日期</view>
                <text>{{ details.time }}</text>
            </view>

            <custom-title bg-type="2">
                游客信息
                <text class="visitors-info-tips">*请添加真实的身份信息</text>
                <custom-button v-if="visitorsList.length > 0" type="info" slot="right"
                    @click="toPage('/pages/pages-gbln/contactManage/manageIndex?isAddUser=true')">选择游客</custom-button>
            </custom-title>
            <view v-if="visitorsList.length === 0" class="choose-visitors tap-style bg-white"
                @click="toPage('/pages/pages-gbln/contactManage/manageIndex?isAddUser=true&checkLength='+visitorsList.length)">
                <view>选择游客</view>
            </view>
            <view v-else class="visitors-list">
                <custom-swipe-action :data="visitorsList" show-divider @toggle="onActionToggle"
                    @right-click="onDelVisitor">
                    <template v-slot="{ row }">
                        <view class="visitor-item">
                            <view class="title">
                                <view>{{ row.name }}</view>
                                <text>{{ row.phone }}</text>
                            </view>
                            <view class="sub-title">
                                <text>身份证</text>
                                <text>{{ row.idCard }}</text>
                            </view>
                        </view>
                    </template>
                </custom-swipe-action>
                <view class="bottom-padding"></view>
            </view>

        </view>
        <custom-bottombar>
            <view class="flex_jus_sb">
                <view class="bottom-bar-price">总计 <text>￥ {{ details.totalAmount }}</text></view>
                <custom-button type="primary" :disabled="visitorsList.length === 0"
                    @click="submitOrder">提交订单</custom-button>
            </view>
        </custom-bottombar>
    </custom-page>
</template>

<script>
import {
    SEC_FONT_COLOR
} from '@/utils/config';
import { encryptIdCard } from "@/utils/index.js";
import { mapState } from 'vuex';
import { checkOrder } from '@/api/ticket';
import { submitOrder } from '@/api/guobaoln';

export default {
    data () {
        return {
            scrollTop: 0,
            details: {
                admissionList: [],
                totalAmount: 0
            },
            visitorsList: []
        };
    },
    computed: {
        ...mapState(['userSelect', 'selectedTickets', 'sourceInfo'])
    },
    onLoad () {
        this.init();
    },
    onShow () {
        this.visitorsList = this.userSelect.map(item => {
            return {
                ...item,
                idCard: encryptIdCard(item.credentialNo),
                show: false
            };
        });
    },
    methods: {
        async init () {
            const data = await checkOrder(this.selectedTickets);
            this.details = data;
        },
        goBack () {
            uni.navigateBack();
        },
        onActionToggle ({ show, index }) {
            this.visitorsList.forEach(item => {
                item.show = false;
            });
            this.visitorsList[index].show = !show;
        },
        onDelVisitor (item) {
            this.visitorsList.splice(item.index, 1);
            this.$store.commit("USER_ADD", this.visitorsList);
        },
        toPage (url) {
            uni.navigateTo({
                url
            });
        },
        async submitOrder () {
            uni.showLoading({
                title: "提交中"
            });
            const obj = {
                ...this.selectedTickets,
                contactsIdList: this.visitorsList.map(item => item.id),
				sourceType: uni.getStorageSync('sourceType') || ''
            };
            console.log(obj);
            const res = await submitOrder(obj);
            uni.hideLoading();
			if(res.type==1){
				this.toPage('/pages/pages-gbln/ticketBooking/confirmOrder?orderSn=' + res.orderSn);
			}
			if(res.type==0){
				uni.reLaunch({
					url:'/pages/pages-gbln/order/details?orderSn=' + res.orderSn
				})
			}
        }
    },
    onPageScroll ({ scrollTop }) {
        this.scrollTop = scrollTop;
    },
    onUnload () {
        this.$store.commit("USER_ADD", []);
    }
}
</script>

<style lang="scss" scoped>
.bg-white {
    background: #fff;
    padding: 0 20rpx;
}

.submit-order-container {
    padding: 0 20rpx;

    .exhibition-hall-name {
        font-weight: bold;
        font-size: 28rpx;
        line-height: 60rpx;
        padding: 10rpx 0 20rpx;
    }

    .ticket-item {
        min-height: 70rpx;
        font-weight: 400;
        font-size: 28rpx;
        gap: 60rpx;

        > view {
            display: flex;
			align-items: center;
            gap: 13rpx;
        }

        .price {
            font-weight: bold;
            font-size: 28rpx;
            color: $sec-font-color;
        }
    }

    .visit-date {
        height: 50rpx;
        padding: 10rpx 20rpx 30rpx;
        border-radius: 0rpx 0rpx 6rpx 6rpx;
        view {
            font-weight: 400;
            font-size: 26rpx;
            color: $tip-font-color;
        }

        text {
            font-weight: 400;
            font-size: 26rpx;
        }
    }

    .visitors-info-tips {
        font-weight: 400;
        font-size: 26rpx;
        color: $sec-font-color;
        margin-left: 20rpx;
    }

    .choose-visitors {
        height: 160rpx;
        font-weight: bold;
        font-size: 28rpx;
        color: $sec-font-color;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 0 6rpx 6rpx;
        > view {
            line-height: 1;
            padding-left: 34rpx;
            @include bgUrl('icon_add_sec.png');
            background-size: 24rpx;
            background-position: left center;
        }
    }

    .visitors-list {
        border-radius: 6rpx;
        overflow: hidden;
        .bottom-padding {
            padding-bottom: 10rpx;
            background-color: #fff;
        }
    }

    .visitor-item {
        display: flex;
        flex-direction: column;
        height: 100rpx;
        justify-content: space-between;
        padding: 8rpx 0;
        box-sizing: border-box;
        > view {
            display: flex;
            color: $tip-font-color;
            font-weight: 400;
            font-size: 26rpx;
            gap: 23rpx;

            &.title > view {
                color: initial;
            }
        }
    }
}
</style>