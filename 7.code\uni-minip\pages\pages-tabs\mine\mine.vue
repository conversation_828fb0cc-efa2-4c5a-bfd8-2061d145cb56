<template>
    <custom-page :safe-area-inset-bottom="false">
        <custom-header :placeholder="false" bg-color="transparent" :show-back-btn="false" :navheight.sync="navHeight">
            <text slot="title" style="color: #fff;">我的</text>
        </custom-header>

        <view class="page-container">
            <view class="user_info_box" :style="{ marginTop: navHeight + 'px' }" @click="checkLogin">
                <image @click="token?gotoImage():checkLogin()" :src="userInfo.avatar || logo" mode="aspectFill"></image>
                <view>
                    <view class="user-name" v-if="!showName">{{ token ? userInfo.name : '请登录'}}
                        <image v-if="token" class="mini-icon tap-style" :src="IMG_URL + 'icon_edit_username.png'"
                            @click="changeUserName"></image>
                    </view>
                    <input v-if="token && showName" class="input" :focus="nameFocus" v-model="userInfo.name"
                        placeholder-class="title_input" type="text" placeholder="请填写" @blur="changeName"
                        :maxlength="10" />

                    <view class="phone">{{ token ? userInfo.phone : '登录后查看更多信息'}}
                        <view v-if="token" class="tap-style" @click="changePhone">
                            <image class="mini-icon" :src="IMG_URL + 'icon_unbind.png'"></image>解绑
                        </view>
                    </view>
                </view>
            </view>
            <view class="order_box">
                <view class="order-title">
                    <view class="area-title">我的订单</view>
                </view>
                <view class="order-cls">
                    <view class="tap-style" @click="toPage('/pages/pages-my/order/order?index=0', true)">
                        <image :src="IMG_URL + '<EMAIL>'"></image>
                        <view>全部订单</view>
                    </view>
                    <view class="tap-style" @click="toPage('/pages/pages-my/order/order?index=1', true)">
                        <image :src="IMG_URL + '<EMAIL>'"></image>
                        <view>待付款</view>
                    </view>
                    <view class="tap-style" @click="toPage('/pages/pages-my/order/order?index=2', true)">
                        <image :src="IMG_URL + '<EMAIL>'"></image>
                        <view>待使用</view>
                    </view>
                </view>
            </view>

            <view class="my-work mb20 tap-style" @click="toPage('/pages/pages-my/myWork/index?isMyGo=true', true)">
                <view class="area-title">我的作品</view>
                <view>查看全部</view>
            </view>

            <view class="more-service">
                <view class="more-service-title">
                    <view class="area-title">更多服务</view>
                </view>
                <custom-cells :data="cellList" @click="onCellClick">
                    <template v-slot="{ cell }">
                        <view :class="[cell.class]">
                            {{ cell.value }}
                        </view>
                    </template>
                </custom-cells>
            </view>

            <tab-bar :value="1" :tabbar-height.sync="tabbarHeight"></tab-bar>
        </view>
    </custom-page>
</template>

<script>
import { IMG_URL, BASE_URL } from '@/utils/config';
import { mapState } from 'vuex';
import { checkLogin } from "@/utils/index.js";
import { phoneNum, updateAvatar, editName } from "@/api/my.js";
import { getQueuePermission } from "@/api/party.js";

export default {
    data () {
        return {
            checkLogin,
            IMG_URL,
            navHeight: 0,
            tabbarHeight: 0,
            cellList: [
                {
                    title: '联系人管理',
                    icon: '<EMAIL>',
                    needLogin: true,
                    path: '/pages/pages-my/contactManage/manageIndex?isAddUser=false'
                },
                {
                    title: '常见问题',
                    icon: '<EMAIL>',
                    path: '/pages/pages-my/question/index'
                },
                {
                    title: '意见反馈',
                    icon: '<EMAIL>',
                    path: '/pages/pages-my/feedback/index',
                    needLogin: true
                },
                {
                    title: '联系客服',
                    icon: '<EMAIL>',
                    noArrow: true,
                    value: '',
                    class: 'cell-phone'
                },/* ,
                {
                    title: '门票核销',
                    icon: '<EMAIL>',
                    needLogin: true,
					path: '/pages/pages-my/writeOff/index'
                } */
                // {
                // 	title: '排队叫号',
                // 	icon: '<EMAIL>',
                // 	// icon: 'lineup-icon.png',
                // 	path: '/pages/pages-index/party/callNumber'
                // }
            ],
            showName: false,//是否修改昵称
            nameFocus: false,//聚焦nameinput
        };
    },
    onLoad () {
        this.getPhone();
        this.getQueue();   //获取叫号权限
        if (this.userInfo.writeOffStatus && this.userInfo.writeOffStatus.data) {
            this.pushWriteOffMenu();
        }
    },
    watch: {
        'userInfo.writeOffStatus': {
            handler (val) {
                if (typeof (val) == 'object' && val.data) {
                    this.pushWriteOffMenu();
                } else if (typeof (val) == 'boolean' && val) {
                    this.pushWriteOffMenu();
                }
            },
            immediate: true
        }
    },
    computed: {
        ...mapState(['token', 'userInfo', 'logo'])
    },
    methods: {
        getQueue () {
            getQueuePermission().then((res) => {
                if (res == true) {
                    this.cellList.push({
                        title: '排队叫号',
                        // icon: '<EMAIL>',
                        icon: 'lineup-icon.png',
                        needLogin: true,
                        path: '/pages/pages-index/party/callNumber'
                    });
                }
            });
        },
        pushWriteOffMenu () {
            this.cellList.push({
                title: '门票核销',
                icon: '<EMAIL>',
                needLogin: true,
                path: '/pages/pages-my/writeOff/index'
            });
        },
        toPage (url, needLogin) {
            if (needLogin && !checkLogin()) return;
            uni.navigateTo({
                url
            });
        },
        onCellClick (cell) {
            cell.path && this.toPage(cell.path, cell.needLogin);
            if (cell.class === 'cell-phone' && cell.value) {
                uni.makePhoneCall({
                    phoneNumber: cell.value,
                    complete: () => { }
                });
            }
        },
        async getPhone () {
            const data = await phoneNum();
            this.cellList[3].value = data;
        },
        // 修改头像
        gotoImage () {
            let that = this;
            // this.freezeSensorsPage() // 在选择图片前冻结神策埋点页面的曝光埋点页面。
            uni.chooseImage({
                count: 1, //默认9
                sizeType: ["original", "compressed"],
                sourceType: ["album", "camera"],
                success: function (res) {
                    const uploadTask = uni.uploadFile({
                        url: BASE_URL.development + 'museum-main/oss/app/upload',
                        filePath: res.tempFilePaths[0],
                        name: 'file',
                        header: {
                            'Authorization': 'Bearer ' + uni.getStorageSync("token")
                        },
                        success: (res) => {
                            let obj = JSON.parse(res.data);
                            if (obj.code == 200) {
                                that.userInfo.avatar = 'http://museum-img.jinhuaze.com/' + obj.data;
                                that.submitOk();
                            } else {
                                // uni.showToast({
                                //     title: 'obj.msg',
                                //     icon: 'none'
                                // });
                            }
                        },
                    });
                },
            });
        },
        //提交页面接口
        async submitOk () {
            let that = this;
            let params = {
                avatar: that.userInfo.avatar,
            };
            const data = await updateAvatar(params);
            // if (data) {
            //     uni.showToast({
            //         title: "修改成功",
            //         icon: "none",
            //     });
            //     this.$store.dispatch("GET_USER_INFO");
            // } else {
            //     uni.showToast({
            //         title: "修改失败",
            //         icon: 'none'
            //     });
            // }
        },
        // 更改手机号
        changePhone () {
            uni.navigateTo({
                url: "/pages/pages-my/changeSelfInfor/changeIndex"
            });
        },
        // 点击编辑btn
        changeUserName () {
            this.showName = true;
            this.nameFocus = true;
        },
        // 修改昵称
        changeName () {
            this.nameFocus = false;
            let params = {
                name: this.userInfo.name
            };
            editName(params).then((res) => {
                if (res) {
                    uni.showToast({
                        title: '修改成功',
                        icon: 'none'
                    });
                    this.showName = false;
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
$radius: 6rpx;
$areaHeight: 120rpx;
.area-title-style {
    height: 120rpx;
    border-radius: $radius;
    display: flex;
    align-items: center;
    background-color: #fff;
}
.page-container {
    @include bgUrl('<EMAIL>');
    padding: 20rpx;
    box-sizing: border-box;
    // border: 1rpx solid red;
}
::v-deep .title_input {
    color: #fff;
}
.input {
    text-align: left;
    vertical-align: top;
    margin-right: 55rpx;
    font-weight: bold;
    font-size: 30rpx;
    width: 80%;
    height: 40rpx;
    margin-bottom: 14rpx;
    flex: 1;
    color: #fff;
    font-family: 'songti' !important;
}
.user_info_box {
    display: flex;
    gap: 40rpx;
    color: #fff;
    padding: 0 40rpx;
    > image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 2rpx solid $border-color;
    }
    > view {
        flex: 1 0 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 10rpx 0;
        .user-name {
            font-weight: bold;
            font-size: 30rpx;
            image {
                width: 24rpx;
                height: 24rpx;
                margin-left: 20rpx;
            }
        }
        .phone {
            font-weight: 400;
            font-size: 28rpx;
            display: flex;
            align-items: center;
            > view {
                margin-left: 20rpx;
                image {
                    width: 22rpx;
                    height: 22rpx;
                    margin-right: 10rpx;
                }
            }
        }
    }
}
.order_box {
    margin: 40rpx 0 20rpx;
    background: #fff;
    background: #ffffff;
    box-shadow: 0rpx 3rpx 10rpx 0rpx rgba(229, 229, 229, 0.2);
    border-radius: $radius;
    padding-bottom: 40rpx;
    .order-title {
        @extend .area-title-style;
        @include bgUrl('order_bg_title.png');
    }
    .order-cls {
        display: flex;
        justify-content: space-between;
        text-align: center;
        padding: 0 20rpx;
        > view {
            flex: 1;
            > view {
                font-weight: 400;
                font-size: 28rpx;
                margin-top: 20rpx;
            }
        }
        image {
            width: 92rpx;
            height: 92rpx;
            display: block;
            margin: 0 auto;
        }
    }
}
.my-work {
    @extend .area-title-style;
    padding-right: 30rpx;
    justify-content: space-between;
}
.more-service {
    border-radius: $radius;
    overflow: hidden;
    &-title {
        @extend .area-title-style;
    }
    .cell-phone {
        display: flex;
        font-weight: bold;
        font-size: 28rpx;
        color: $sec-font-color;
        padding-left: 30rpx;
        @include bgUrl('icon_phone.png');
        background-size: 22rpx;
        background-position: left center;
    }
}
</style>
