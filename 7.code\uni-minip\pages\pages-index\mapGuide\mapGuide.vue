<template>
	<custom-page :safe-area-inset-bottom="false">
		<custom-header title="地图导览"></custom-header>
		<image :src="mapImg" mode="widthFix" @click="previewImage"></image>
	</custom-page>
</template>

<script>
	import { getMapGuide } from '@/api/home';
	import { UPLOAD_IMG_URL } from '@/utils/config';
	export default {
		data() {
			return {
				mapImg: ''
			};
		},
		onLoad() {
			this.init()
		},
		methods: {
			async init() {
				const data = await getMapGuide()
				this.mapImg = UPLOAD_IMG_URL + data
			},
			previewImage() {
				uni.previewImage({
					urls: [this.mapImg]
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	image {
		width: 100%;
		// height: auto;
	}
</style>
