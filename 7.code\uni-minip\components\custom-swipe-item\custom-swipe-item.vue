<template>
	<view class="custom-swipe-item">
		<view class="swipe-container">
			<view
				class="swipe-content"
				:style="{ 
					transform: `translateX(${translateX}px)`, 
					transition: isAnimating ? 'transform 0.3s ease' : 'none' 
				}"
				@touchstart="onTouchStart"
				@touchmove="onTouchMove"
				@touchend="onTouchEnd"
				@click="onContentClick"
			>
				<slot></slot>
			</view>
			<view 
				class="delete-btn" 
				:style="{
					width: deleteButtonWidth + 'px',
					top: deleteButtonTop + 'rpx',
					height: deleteButtonHeight + 'rpx'
				}" 
				@click="onDeleteClick"
			>
				<slot name="delete">
					<text>删除</text>
				</slot>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'custom-swipe-item',
	props: {
		// 是否禁用滑动（比如在管理模式下）
		disabled: {
			type: <PERSON>olean,
			default: false
		},
		// 删除按钮宽度
		deleteButtonWidth: {
			type: Number,
			default: 80
		},
		// 索引，用于标识当前项
		index: {
			type: Number,
			default: 0
		},
		// 删除按钮顶部偏移量（rpx）
		deleteButtonTop: {
			type: Number,
			default: 16
		},
		// 删除按钮高度（rpx）
		deleteButtonHeight: {
			type: Number,
			default: 200
		}
	},
	data() {
		return {
			translateX: 0,
			isAnimating: false,
			// 触摸相关数据
			touchData: {
				startX: 0,
				startY: 0,
				currentX: 0,
				currentY: 0,
				isMoving: false
			}
		};
	},
	methods: {
		// 触摸开始
		onTouchStart(e) {
			if (this.disabled) return;

			const touch = e.touches[0];
			this.touchData.startX = touch.clientX;
			this.touchData.startY = touch.clientY;
			this.touchData.currentX = touch.clientX;
			this.touchData.currentY = touch.clientY;
			this.touchData.isMoving = false;

			// 通知父组件有项目开始滑动，用于关闭其他已打开的项目
			this.$emit('swipe-start', this.index);
		},
		
		// 触摸移动
		onTouchMove(e) {
			if (this.disabled) return;

			const touch = e.touches[0];
			this.touchData.currentX = touch.clientX;
			this.touchData.currentY = touch.clientY;

			const deltaX = this.touchData.currentX - this.touchData.startX;
			const deltaY = this.touchData.currentY - this.touchData.startY;

			// 判断是否为水平滑动
			if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
				this.touchData.isMoving = true;
				e.preventDefault(); // 阻止页面滚动

				if (deltaX < 0) {
					// 向左滑动：显示删除按钮
					const translateX = Math.max(deltaX, -this.deleteButtonWidth);
					this.translateX = translateX;
				} else {
					// 向右滑动：关闭删除按钮
					if (this.translateX < 0) {
						// 如果当前已经有偏移，向右滑动时逐渐恢复到0
						const translateX = Math.min(this.translateX + deltaX, 0);
						this.translateX = translateX;
					} else {
						// 如果当前没有偏移，向右滑动不做任何操作
						this.translateX = 0;
					}
				}
			}
		},
		
		// 触摸结束
		onTouchEnd(e) {
			if (this.disabled) return;

			const deltaX = this.touchData.currentX - this.touchData.startX;

			if (this.touchData.isMoving) {
				// 设置动画
				this.isAnimating = true;

				if (deltaX < 0) {
					// 向左滑动
					if (Math.abs(deltaX) > this.deleteButtonWidth / 2) {
						// 滑动距离超过阈值，显示删除按钮
						this.translateX = -this.deleteButtonWidth;
					} else {
						// 滑动距离不够，恢复原位
						this.translateX = 0;
					}
				} else {
					// 向右滑动，直接关闭删除按钮
					this.translateX = 0;
				}

				// 动画结束后移除动画状态
				setTimeout(() => {
					this.isAnimating = false;
				}, 300);
			}

			// 重置触摸数据
			this.touchData.isMoving = false;
		},
		
		// 内容点击事件
		onContentClick() {
			if (!this.touchData.isMoving) {
				this.$emit('click', this.index);
			}
		},
		
		// 删除按钮点击事件
		onDeleteClick() {
			this.$emit('delete', this.index);
		},
		
		// 关闭滑动状态（供父组件调用）
		close() {
			if (this.translateX < 0) {
				this.isAnimating = true;
				this.translateX = 0;
				setTimeout(() => {
					this.isAnimating = false;
				}, 300);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.custom-swipe-item {
	position: relative;
	overflow: hidden;
	
	.swipe-container {
		position: relative;
		height: 100%;
	}
	
	.swipe-content {
		position: relative;
		z-index: 2;
		// background-color: #fff;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		right: 0;
		background-color: #dd524d;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1;
		border-radius: 0 10rpx 10rpx 0; // 右侧圆角

		text {
			color: #ffffff;
			font-size: 28rpx;
			font-weight: bold;
		}
	}
}
</style>
