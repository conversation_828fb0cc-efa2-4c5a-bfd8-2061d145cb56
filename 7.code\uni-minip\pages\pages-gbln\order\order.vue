<template>
	<custom-page :empty="orderList.length === 0">
		<custom-header title="我的订单" show-loading>
			<view class="tabs-view" slot="bottom">
				<u-tabs
				:current="current"
				 :list="tabs"
				 itemStyle="padding: 20rpx 40rpx; height: 59rpx;"
				 lineWidth="60rpx"
				 :lineColor="SEC_FONT_COLOR"
				 lineHeight="5rpx"
				 :inactiveStyle="`font-size:26rpx;color: color: ${TIP_FONT_COLOR};font-weight: 400`"
				 :activeStyle="`font-size:30rpx;color: color: ${MAIN_FONT_COLOR};font-weight: bold`"
				 @change='onTabChange'
				 ></u-tabs>
			</view>
		</custom-header>
		
		<view class="order-list">
			<view class="order-item" v-for="(item, index) in orderList" :key="index">
				<custom-title bg-type="2">
					{{ item.orderCategory === 2 ? sourceInfo.hallTitle : item.exhibitionName }}
					<view slot="right" class="order-status-tag" :class="{black: blackTextStatus.includes(item.orderStatus)}">{{ item.orderStatusText }}</view>
				</custom-title>
				<view class="content">
					<view class="flex_jus_sb" v-for="(ticket, ticketIndex) in item.orderAdmission" :key="ticketIndex">
						<view>{{ ticket.admissionName }}</view>
						<custom-number>{{ ticket.buyNum }}</custom-number>
					</view>
				</view>
				<view class="date">
					<view>参观日期：{{ item.admissionDateStr }}</view>
					<view>参观人数：{{ item.visitorsNum }}人</view>
				</view>
				<view class="bottom-bar flex_jus_sb">
					<view class="total-price">￥ {{ item.totalAmount }}</view>
					<view class="buttons">
						<custom-button v-if="canCancel.includes(item.orderStatus)" type="info" @click="openCancelModal(item)">取消订单</custom-button>
						<custom-button type="primary" @click="toPage('/pages/pages-gbln/order/details?orderSn=' + item.orderSn)">查看订单</custom-button>
					</view>
				</view>
			</view>
		</view>
		<custom-modal :visible.sync="cancelModalStatus" title="确定要取消此订单吗？" @confirm="onCancelOrder"></custom-modal>
	</custom-page>
</template>

<script>
	import { MAIN_FONT_COLOR, SEC_FONT_COLOR, TIP_FONT_COLOR } from '@/utils/config';
	import { getOrderList, closeOrder } from '@/api/order';
	import statusMap from "./js/orderStatusMap.js"
	import { GBLN_CONFIG } from '@/utils/guobaoln';
	
	export default {
		data() {
			return {
				GBLN_CONFIG,
				MAIN_FONT_COLOR,
				SEC_FONT_COLOR,
				TIP_FONT_COLOR,
				current: 0,
				tabs: [{
					name: '全部',
					orderStatus: ''
				}, {
					name: '待付款',
					orderStatus: statusMap.PENDING_PAYMENT
				}, {
					name: '待使用',
					orderStatus: statusMap.TO_BE_USED
				}],
				// 100待付款 101订单关闭 200待使用 201已完成 300退款中 301已退款 302退款失败
				
				// 黑色的订单状态字体
				blackTextStatus: [statusMap.CLOSED, statusMap.COMPLETED, statusMap.REFUNDED, statusMap.REFUND_FAILED, statusMap.EXPIRED],
				// 显示取消订单按钮
				canCancel: [statusMap.PENDING_PAYMENT],
				
				pageForm: {
					pageNum: 1,
					pageSize: 10,
					orderStatus: '', //筛选状态 订单状态 100待付款 200待使用
		
				},
				total: 0,
				orderList: [],
				// 取消订单
				cancelModalStatus: false,
				cancelOrderItem: {}
			};
		},
		computed: {
			sourceInfo() {
				return this.$store.state.sourceInfo || {};
			}
		},
		onLoad({ index }) {
			this.current = index
			this.pageForm.orderStatus = this.tabs[index].orderStatus
		},
		onShow() {
			this.pageForm.pageNum = 1
			this.init(true)
		},
		methods: {
			async init(resetList) {
				const { rows, total } = await getOrderList(this.pageForm)
				console.log("订单列表", rows)
				if (resetList) this.orderList = []
				this.orderList.push(...rows)
				this.total = total
			},
			onTabChange({ orderStatus }) {
				this.pageForm.pageNum = 1
				this.pageForm.orderStatus = orderStatus
				this.init(true)
			},
			toPage(url) {
				uni.navigateTo({
					url
				})
			},
			openCancelModal(item) {
				this.cancelOrderItem = item
				this.cancelModalStatus = true
			},
			async onCancelOrder() {
				await closeOrder({ orderSn: this.cancelOrderItem.orderSn })
				uni.showToast({
					title: '取消成功',
					icon: 'none'
				})
				this.pageForm.pageNum = 1
				this.init(true)
			}
		},
		async onPullDownRefresh() {
			this.pageForm.pageNum = 1
			await this.init(true)
			setTimeout(() => {
				uni.stopPullDownRefresh()
			}, 1000)
		},
		async onReachBottom() {
			if (this.orderList.length === this.total) return
			console.log("滚动到底部")
			this.pageForm.pageNum ++
			await this.init()
		}
	}
</script>

<style lang="scss" scoped>
	.tabs-view {
		border-bottom: 1rpx solid #D0C9BC;
		// position: sticky;
		background-color: $main-color;
		padding: 0 20rpx;
		// z-index: 999;
		::v-deep {
			.u-tabs__wrapper__nav__item__text {
				transition: all .2s;
			}
			.u-tabs__wrapper__nav__line {
				bottom: 0;
				border-radius: 0;
			}
		}
	}

	.order-list {
		padding: 30rpx 20rpx;
	}
	.order-item {
		border-radius: 6rpx;
		overflow: hidden;
		&+.order-item {
			margin-top: 20rpx;
		}
		.order-status-tag {
			width: 121rpx;
			height: 53rpx;
			@include bgUrl("order_status_bg.png");
			text-align: center;
			line-height: 53rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: $sec-font-color;
			margin-right: 10rpx;
			&.black {
				color: $tip-font-color;
			}
		}
		.content {
			background-color: #fff;
			padding: 0 20rpx 10rpx;
			> view {
				line-height: 70rpx;
				font-weight: bold;
				font-size: 28rpx;
				> view {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
		.date {
			background-color: #fff;
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 24rpx;
			color: $tip-font-color;
			padding: 13rpx 20rpx;
			view:last-child {
				margin-left: 60rpx;
				position: relative;
				&:before {
					display: block;
					content: '';
					width: 3rpx;
					height: 20rpx;
					background: #D1C9BA;
					position: absolute;
					left: -30rpx;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
		.bottom-bar {
			background-color: #fff;
			padding: 22rpx 30rpx 22rpx 20rpx;
			.total-price {
				font-weight: bold;
				font-size: 32rpx;
				color: $sec-font-color;
			}
			.buttons {
				display: flex;
				gap: 30rpx;
			}
		}
	}
</style>