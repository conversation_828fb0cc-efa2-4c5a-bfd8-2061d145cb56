<template>
	<custom-page bg="notice-bg.png" :empty="datalist.length === 0">
	    <custom-header title="核销记录" bg="notice-bg.png" :show-loading="true" :navheight.sync="navHeight">
			<view style="padding: 20rpx 0 10rpx;" slot="bottom">
				<view class="ser">
					<input class="input" v-model="name" type="text" @input="getlist" placeholder="请输入游客姓名" placeholder-class="inputClass" />
					<view class="sertext" @click="ser">搜索</view>
				</view>
			</view>
		</custom-header>
		<view class="list" v-if="datalist!=''">  
			<view class="scroll">
			<!-- <scroll-view class="scroll" scroll-y @scrolltolower="lowerBottom"
				:refresher-threshold='100' refresher-enabled :refresher-triggered="triggered" @refresherrefresh="onRefresh"> -->
					<view class="list-box" v-for="(item,index) in datalist">
						<view class="listbox-date">{{item.date}}</view>
						<view class="listbox-list" v-for="(a,b) in item.list">
							<view class="lbList-top">
								<view class="lines"></view> 
								<view class="texts">{{ a.orderCategory === 2 ? sourceInfo.hallTitle : a.exhibitionName }}{{ a.orderSource == 4 ? '工作票' : a.orderSource == 6 ? '现场票':'门票' }}</view>
							</view>
							<view v-if="a.orderSource !== 4&&a.orderSource !== 6" class="lbList-name">
								<view class="lbListName-l">游客姓名</view>
								<view class="lbListName-r">{{a.contactsName}}</view>
							</view>
							<view class="lbList-time">
								<view class="lbListName-l">核销时间</view>
								<view class="lbListName-r">{{a.verifincationTime}}</view>
							</view>
							<view class="lbList-details">
								<view class=""></view>
								<custom-button type="primary" @click="details(a.id)">详情</custom-button>
							</view>
						</view>
					</view>
			<!-- </scroll-view> -->
			</view>
		</view>
	</custom-page>
</template>
<script>

	import {writeList} from "@/api/my.js"
	export default {
		data() {
			return {
				triggered:false,
				navHeight:0,
				pageNum:1,
				pageSize:5,
				total:0,
				name:'',
			    datalist:[]
			};
		},
		computed: {
			sourceInfo() {
				return this.$store.state.sourceInfo || {};
			}
		},
		onLoad(){
			this.getlist()
		},
		methods:{
			ser(){
				this.getlist()
			},
			getlist(){
				let params = {
					name:this.name,
					// pageNum:this.pageNum,
					// pageSize:this.pageSize
				}
				writeList(params).then((res)=>{
					// this.datalist =  res.rows.map((i,e)=>{
					// 	let list = []
					// 	return {
					// 		date:i.createTime,
					// 		list: res.rows
					// 	}
					// })
					this.datalist = res
					setTimeout(() => {
						uni.stopPullDownRefresh()
					}, 1000)
				})
			},
			details(v){
				uni.navigateTo({
					url:'/pages/pages-my/writeOff/hexiaoInfo?id='+ v
				})
			},
			onRefresh (v) {
				// this.datalist = []
				this.pageNum = 1;
				if (!this.triggered) {
				    this.triggered = true;
				}
				setTimeout(() => {
				    this.triggered = false;	
					this.getlist();
				}, 100);
			},
			lowerBottom(){
				if (this.pageNum * this.pageSize >= this.total) {
				    return false;
				}
				this.pageNum++;
				uni.showLoading({
					title: '加载中'
				}); 
				setTimeout(function () {
					uni.hideLoading();
				}, 1000);
				setTimeout(() => {
					this.getlist()
				}, 1000);
			}
		},
		onReachBottom() {
			this.lowerBottom()
		},
		onPullDownRefresh() {
			this.onRefresh()
		}
	}
</script>
<style lang="scss" scoped>
.ser{
	width: calc(100% - 40rpx);
	height: 80rpx;
	// margin: 30rpx 20rpx 10rpx 20rpx;
	margin: 0 auto;
	@include bgUrl('write-serchBg.png');
	background-size: 100% 100%;
	position: relative;
	.sertext{
		position: absolute;
		top: 20rpx;
		right: 30rpx;
		font-weight: bold;
		font-size: 28rpx;
		color:$sec-font-color;
	}
	.input{
		width: calc(100% - 150rpx);
		height: 100%;
		margin-left: 30rpx;
		margin-right: 120rpx;
	}
	/deep/ .inputClass{
		font-weight: 400;
		font-size: 26rpx;
		color: #B2AFAF;
	}
}

.list{
	width: calc(100% - 40rpx);
	margin-left: 20rpx;
	margin-top: 20rpx;
	.scroll{
		width: 100%;
		height: 100%;
		.list-box{
			width: 100%;
			margin: 0rpx 0rpx 10rpx 0rpx;
			.listbox-date{
				height: 30rpx;
				line-height: 30rpx;
				margin-top: 30rpx;
				margin-bottom: 30rpx;
				font-weight: bold;
				font-size: 30rpx;
				color: $main-font-color;
			}
			.listbox-list{
				width: 100%;
				// height: 340rpx;
				background-color: #ffffff;
				margin-bottom: 20rpx;
				.lbList-top{
					width: 100%;
					height: 120rpx;
					display: flex;
					align-items: center;
					@include bgUrl('writeOff-titleBg.png');
					background-size: 100% 100%;
					overflow: hidden;
					.lines{
						width: 10rpx;
						height: 2rpx;
						background: #333030;
						margin-left: 22rpx;
						margin-right: 11rpx;
					}
					.texts{
						font-weight: bold;
						font-size: 30rpx;
						color: $main-font-color;
					}
				}
				.lbList-name{
					width: calc(100% - 40rpx);
					height: 50rpx;
					margin-left: 20rpx;
					margin-top: 10rpx;
					margin-bottom: 10rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					.lbListName-l{
						font-weight: 400;
						font-size: 26rpx;
						color: $tip-font-color;
					}
					.lbListName-r{
						font-weight: 400;
						font-size: 26rpx;
						color: $main-font-color;
					}
				}
				.lbList-time{
					width: calc(100% - 40rpx);
					height: 50rpx;
					margin-left: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					.lbListName-l{
						font-weight: 400;
						font-size: 26rpx;
						color: $tip-font-color;
					}
					.lbListName-r{
						font-weight: 400;
						font-size: 26rpx;
						color: $main-font-color;
					}
				}
				.lbList-details{
					width: calc(100% - 60rpx);
					margin-left: 30rpx;
					height: 100rpx;
					background: #FFFFFF;
					border-radius: 0rpx 0rpx 6rpx 6rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
		}
	}
}
</style>


