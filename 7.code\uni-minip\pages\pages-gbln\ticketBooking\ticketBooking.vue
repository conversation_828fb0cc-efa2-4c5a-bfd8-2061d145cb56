<template>
	<custom-page bg="ticket_booking_bg.png">
		<custom-header :title="sourceInfo.title" :hide-home-btn="false" :scroll-top="scrollTop" :scroll-ratio="2">
			<view class="order-btn tap-style" slot="left" @click="toPage('/pages/pages-gbln/order/order?index=0')">
				<u-icon name="order" color="#000" size="28"></u-icon>
				我的订单
			</view>
		</custom-header>

		<view class="page-container">
			<!-- 展厅信息 -->
			<view class="exhibition-hall-information">
				<view class="poster">
					<u-swiper :list="banner" height="400rpx" radius="4rpx" :autoplay="true" interval="5000" @click='onSwiperClick'></u-swiper>
				</view>
				<view class="name-box">
					<view class="name flex_jus_sb">
						<view>{{ sourceInfo.title }}</view>
						<!-- <button class="u-reset-button" open-type="share"></button> -->
					</view>
					<view class="desc-btn tap-style" @click="toPage('/pages/pages-gbln/ticketBooking/showNotice')">购票须知
					</view>
				</view>
			</view>
			<!-- 参观日期 -->
			<view class="bg-white mt20">
				<custom-title bg-type="1">参观日期</custom-title>
				<date-time-picker v-model="currentSelectDate" readonly-tag="已约满" tag="可预约" :data="dateData" show-more
					full-screen :formatter="dateFormatter" @input="changeDate"></date-time-picker>
				<custom-title>参观时间</custom-title>
				<date-time-picker v-model="currentSelectTime" :data="timeData" :disabled="!currentSelectDate.date"
					:count="-1" item-width="200rpx" @input='changeTime'></date-time-picker>
				<custom-title>选择票种</custom-title>
				<view class="ticket-select">
					<view class="ticket-select_item" v-for="(item, index) in ticketList" :key="index">
						<view class="title flex_jus_sb">
							<view>{{ item.admissionName }}</view>
							<!-- 总的数量 + 当前的visitCount > 总票数 => 禁用 -->
							<custom-number-box v-model="item.num" :max="999999999"
								:not-tip="!currentSelectDate.date || !currentSelectTime.date"
								:disabled-add="!currentSelectTime.date || maxTicketDisabled || maxTicketCount + item.visitCount > 20 || maxTicketCount + item.visitCount > admissionsNum"></custom-number-box>
						</view>
						<view class="desc">{{ item.admissionRemark }}</view>
						<view class="tags">{{ item.tags }}</view>
						<view class="price">￥ {{ item.price }}</view>
					</view>
				</view>
			</view>
		</view>
		<custom-bottombar>
			<view class="flex_jus_sb">
				<view class="bottom-bar-price">总计 <text>￥ {{totalPrice }}</text></view>
				<custom-button type="primary" :disabled="disabledNext" @click="goNextStep">下一步</custom-button>
			</view>
		</custom-bottombar>
	</custom-page>
</template>

<script>
	import DateTimePicker from "./components/dateTimePicker.vue";
	import { getGblnTicketBookingInfo } from '@/api/guobaoln.js'
	import {
		GBLN_CONFIG
	} from '@/utils/guobaoln';
	export default {
		components: {
			DateTimePicker
		},
		data() {
			return {
				scrollTop: 0,
				details: {},
				admissionsNum: 0,
				admissionDateID: null,
				banner: [
					{
						url: GBLN_CONFIG.img
					},
					{
						url: 'https://museum-img.jinhuaze.com/guobao/home_banner.jpg'
					}
				],
				currentSelectDate: {},
				dateData: [],
				currentSelectTime: {},
				originTimeData: [], // 原始时间
				timeData: [],
				ticketList: []
			};
		},
		computed: {
			totalPrice() {
				return this.ticketList.reduce((total, item) => {
					return total + item.price * item.num
				}, 0).toFixed(2)
			},
			maxTicketDisabled() {
				return this.ticketList.reduce((total, item) => {
					return total + item.num
				}, 0) === 20
			},
			maxTicketCount() {
				return this.ticketList.reduce((total, item) => {
					return total + item.num * item.visitCount
				}, 0)
			},
			disabledNext() {
				return JSON.stringify(this.currentSelectDate) == '{}' || JSON.stringify(this.currentSelectTime) == '{}' ||
					this.checkSelect()
			},
			sourceInfo() {
				return this.$store.state.sourceInfo || {};
			}
		},
		watch: {
			currentSelectDate: {
				handler() {
					this.currentSelectTime = {}
					this.parseTimeData()
				},
				deep: true,
				immediate: true
			}
		},
		onLoad() {
			this.init()
		},
		methods: {
			onSwiperClick(index) {
				if (index === 1) {
					uni.switchTab({
						url: '/pages/pages-tabs/home/<USER>'
					})
				}
			},
			async init() {
				const {
					dateList,
					timeList,
					digitalShowroom,
					admissionList,
					bannerList
				} = await getGblnTicketBookingInfo()
				this.details = digitalShowroom
				// this.admissionsNum = admissionsNum
				this.dateData = dateList.map(item => {
					item.readonly = item.admissionsNum < 1
					return item
				})
				if (!dateList[0].disabled && dateList[0].admissionsNum > 0) {
					this.currentSelectDate = dateList[0]
				}
				// this.admissionsNum = this.dateData[0]?.timeListGB[0].admissionsNum
				this.originTimeData = dateList[0]?.timeListGB
				this.ticketList = admissionList.map(item => {
					return {
						...item,
						tags: [item.serviceLifeName, item.refundRulesName].join(" | "),
						num: 0
					}
				})
				this.parseTimeData()
			},
			parseTimeData() {
				if (!this.originTimeData) return
				this.timeData = this.originTimeData.map(item => {
					const curDate = +new Date(this.currentSelectDate.date + ' ' + item.endTime)
					return {
						...item,
						date: item.timeFormat,
						label: item.timeFormat,
						disabled: +new Date() > curDate
					}
				})
			},
			toPage(url) {
				uni.navigateTo({
					url
				});
			},
			//下一步
			goNextStep() {
				console.log(this.currentSelectDate)
				const obj = {
					admissionDate: this.currentSelectDate.date,
					admissionStartTime: this.currentSelectTime.startTime,
					admissionEndTime: this.currentSelectTime.endTime,
					admissionDateID: this.admissionDateID,
					admissionList: this.ticketList.filter(f => f.num > 0).map(item => {
						return {
							admissionId: item.id,
							quantity: item.num
						}
					}),
				}
				console.log(JSON.parse(JSON.stringify(obj)))
				this.$store.commit("SET_TICKETS", obj)
				this.toPage('/pages/pages-gbln/ticketBooking/submitOrder')
			},
			changeDate(e) {
				console.log('changeDate', e)
				this.admissionsNum = e.timeListGB[0].admissionsNum
				this.admissionDateID = e.timeListGB[0].admissionDateID
				this.ticketList = this.ticketList.map(item => {
					return {
						...item,
						num: 0
					}
				})
				this.originTimeData = e.timeListGB
				this.parseTimeData();
				this.$nextTick(() => {
					for (var i = 0; i < this.timeData.length; i++) {
						if (!this.timeData[i].disabled) {
							this.currentSelectTime = this.timeData[i]
							return
						}
					}
				})
				this.resetTicketNum()
			},
			changeTime(e) {
				console.log('changeTime', e)
				this.admissionsNum = e.admissionsNum
				this.admissionDateID = e.admissionDateID
				this.resetTicketNum()
			},
			resetTicketNum() {
				this.ticketList.forEach(item => {
					item.num = 0
				})
			},
			checkSelect() {
				var select = false
				this.ticketList.forEach((item, index) => {
					if (item.num > 0) {
						select = true
					}
				})
				return !select
			},
			dateFormatter(day) {
				if (day.readonly) {
					day.bottomInfo = '已约满'
				}
				return day
			}
		},
		onPageScroll({
			scrollTop
		}) {
			this.scrollTop = scrollTop;
		},
		/* onShareAppMessage: function() {
			return {
				title: this.GblnConfig.title + '门票预订',
				path: '/pages/pages-gbln/ticketBooking/ticketBooking'
			};
		},
		onShareTimeline: function() {
			return {
				title: this.GblnConfig.title + '门票预订',
				path: '/pages/pages-gbln/ticketBooking/ticketBooking'
			};
		} */
	}
</script>

<style lang="scss" scoped>
	.page-container {
		padding: 0 20rpx;
	}
	.order-btn {
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		color: #000;
	}

	.radius {
		border-radius: 6rpx;
		overflow: hidden;
	}

	.common-title {
		height: 120rpx;
		display: flex;
		align-items: center;

		&.order-bg {
			@include bgUrl('order_bg_title.png');
		}
	}

	.bg-white {
		background-color: #fff;
		@extend .radius;
	}

	.exhibition-hall-information {
		@extend .radius;

		.poster {
			height: 400rpx;
			background-color: $border-color;
			border-radius: 6rpx 6rpx 0rpx 0rpx;
			border: 4px solid $border-color;
		}

		.name-box {
			padding: 0 20rpx 28rpx;
			background-color: #fff;
		}

		.name {
			padding: 16rpx 0;
			font-weight: bold;
			font-size: 30rpx;

			>view {
				flex: 1;
			}

			button {
				width: 50rpx;
				height: 50rpx;
				@include bgUrl('icon_share.png');
				background-size: 26rpx;
				background-position: center;
			}
		}

		.desc-btn {
			font-weight: bold;
			font-size: 28rpx;
			color: $sec-font-color;
			padding-bottom: 10rpx;
		}
	}

	.ticket-select {
		padding: 0 30rpx;

		&_item {
			border-top: 1rpx solid #d0c9bc;
			padding-bottom: 30rpx;

			.title {
				padding: 30rpx 0 17rpx;
				font-weight: bold;
				font-size: 28rpx;
				line-height: 1;
			}

			.desc {
				font-weight: 400;
				font-size: 26rpx;
				line-height: 50rpx;
			}

			.tags {
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				padding: 10rpx 0;
			}

			.price {
				font-weight: bold;
				font-size: 32rpx;
				color: $sec-font-color;
				padding-top: 16rpx;
			}
		}
	}
</style>