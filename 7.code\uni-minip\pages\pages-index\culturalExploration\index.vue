<template>
	<custom-page bg="cultural-bg.png">
	    <custom-header title="文化探索" bg-color="transparent" :navheight.sync="navHeight"></custom-header>
	    <view class="swiperBorder">
			<view class="swiper-child"></view>
			<view class="swiper-child2"></view>
			<u-swiper
			    @click="swiperIndex"
				:list="swiperList"
				indicator
				indicatorMode="line"
				circular
			></u-swiper>
		</view>
		<view class="list" :style="{height:`calc(100vh - ${navHeight}px - 580rpx)`}">
			<scroll-view class="scroll" scroll-y @scrolltolower="lowerBottom"
			    :refresher-threshold='100' refresher-enabled :refresher-triggered="triggered" @refresherrefresh="onRefresh">	
				 <view class="list-child" v-for="(item,index) in datalist" @click="details(item.id)">
					 <view class="list-box">
						 <view class="list-img">
							 <image class="img" :src="item.articleCover" mode="aspectFill"></image>
							 <view class="text">{{item.type | type}}</view>
						 </view>
						 
						 <view class="list-content">
							 <view class="title">{{item.articleTitle}}</view>
							 <view v-if="item.content&&item.content!=''" class="content" v-html="item.content.replace(/<img.*\/>/ig, '').replace(/<[^>]+>/g, '').substring(0,20)"></view>
						 </view>
					 </view>
				 </view>
			</scroll-view>
		</view>
		
		
		
		
	</custom-page>
</template>

<script>
	import {UPLOAD_IMG_URL} from "@/utils/config.js"
    import {culturList,culturSwiper} from "@/api/home.js"
	export default {
		data() {
			return {
				navHeight:0,
				datalist:[],
				swiperList: [],
				triggered:false,
				pageNum:1,
				pageSize:5,
				total:0,
				swiperItem:[]
			};
		},
		filters:{
			type(v){
				let str = ''
				if(v=='1'){
					str = '文化探索'
				}else if(v=='2'){
					str = '攻略'
				}
				return str
			}
		},
		methods:{
			async getlist(){
				let data = {
					pageNum:this.pageNum,
					pageSize:this.pageSize
				}
				const { rows,total } = await culturList(data)
				this.total = total
				if(total <= '5'){
					this.datalist = rows
				}else if(total > '5'){
					this.datalist = [...this.datalist,...rows]
				} 
			},
			async swiper(){
				const  data  = await culturSwiper()
				let lists = data.map((item,index)=>{
					return UPLOAD_IMG_URL + item.fileUrl
				})
				this.swiperItem = data
				this.swiperList = lists
			},
			swiperIndex(index){
				uni.navigateTo({
				  url: this.swiperItem[index].fileRoute,
				});
			},
		    lowerBottom(){
				 if (this.pageNum * this.pageSize >= this.total) {
				     return false;
				 }
				 this.pageNum++;
				 uni.showLoading({
				 	title: '加载中'
				 }); 
				 setTimeout(function () {
				 	uni.hideLoading();
				 }, 1000);
				 setTimeout(() => {
				 	this.getlist()
				 }, 1000);
			},
			onRefresh (v) {
				this.datalist = []
				this.pageNum = 1;
				if (!this.triggered) {
				    this.triggered = true;
				}
				setTimeout(() => {
				    this.triggered = false;	
					this.getlist();
				}, 1000);
			},
			details(id){
				console.log(id)
				uni.navigateTo({
					url:'/pages/pages-index/culturalExploration/details?infoId='+ id
				})
			}
		},
		onLoad(){
			this.getlist()
			this.swiper()
		}
	}
</script>

<style lang="scss">
.swiperBorder{
	width: calc(100% - 40rpx);
	margin-left: 20rpx;
	height: 484rpx;
	background-color: transparent;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	margin-top: -20rpx;
	.swiper-child{
		width: calc(100% - 20rpx);
		height: 6rpx;
		position: absolute;
		top: 442rpx;
		left: 10rpx;
		background: #666363;
		opacity: 0.2;
	}
	.swiper-child2{
		width: calc(100% - 40rpx);
		height: 6rpx;
		position: absolute;
		top: 448rpx;
		left: 20rpx;
		background: #666363;
		opacity: 0.2;
	}
	::v-deep .u-swiper {
		height: 100% !important;
		background: transparent !important;
	}
	::v-deep .u-swiper__wrapper{
		height: 400rpx !important;
		background: transparent !important;
		border-radius: 6rpx 6rpx 6rpx 6rpx;
		border: 4rpx solid #FFFFFF;
		box-sizing: border-box;
	}
	::v-deep .u-swiper__wrapper__item__wrapper__image{
		height: 400rpx!important;
	}
	::v-deep .u-swiper__indicator{
		background: #D0C9BC !important;
		border-radius: none !important;
		height: 4rpx !important;
		bottom: 6rpx !important;
	}
	::v-deep .u-swiper-indicator__wrapper--line__bar{
		background-color: #B36859 !important; 
		height: 10rpx !important;
		margin-top: -4rpx !important;
		border-radius: 0 !important;
	}
}
.list{
	margin-top: 20rpx;
	width: calc(100% - 40rpx);
	margin-left: 20rpx;
	overflow-y: auto;
	.scroll{
		width: 100%;
		height: 100%;
		.list-child{
			width: 100%;
			height: 204rpx;
			@include bgUrl('cultural-bglist.png');
			background-size: 100% 100% !important;			
			margin-bottom: 20rpx;
			overflow: hidden;
			.list-box{
				width: calc(100% - 50rpx);
				margin-left: 20rpx;
				height: 160rpx;
				margin-top: 22rpx;
				display: flex;
				justify-content: space-between;
				.list-img{
					width: 160rpx;
					height: 160rpx;
					position: relative;
					border-radius: 6rpx;
					border: 2px solid #D0C9BC;
					overflow: hidden;
					.img{
						width: 160rpx;
						height: 160rpx;
						border-radius: 4rpx;
					}
					.text{
						position: absolute;
						bottom: 0;
						right: 0;
						display: flex;
						justify-content: center;
						align-items: center;
						background: linear-gradient(0deg, rgba(218,186,139,0.8) 0%, #F7DFC1 100%);
						border-radius: 4rpx 0rpx 4rpx 0rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: $main-font-color;
						padding: 5rpx 10rpx;
					}
				}
				.list-content{
					width: calc(100% - 180rpx);
					height: 160rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					.title{
						font-weight: 500;
						font-size: 28rpx;
						color: $main-font-color;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

					}
					.content{
						font-weight: 400;
						font-size: 24rpx;
						color: $tip-font-color;
					}
				}
			}
		}
	}
	
}
</style>
