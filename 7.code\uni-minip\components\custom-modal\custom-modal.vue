<template>
	<view class="custom-modal">
		<u-popup :show="visible" round="6rpx" mode="center" :safeAreaInsetBottom="false" overlayOpacity="0.4"
			:closeOnClickOverlay="false" @close="close">
			<view class="custom-modal_inner">
				<view class="title">
					<slot name="title">{{ title }}</slot>
				</view>

				<view class="bottom-bar">
					<slot name="bottomBar">
						<custom-button type="info" @click="onCancel">{{ cancelText }}</custom-button>
						<custom-button type="primary" @click="onConfirm">{{ confirmText }}</custom-button>
					</slot>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		name:"custom-modal",
		props: {
			visible: Boolean,
			title: String,
			cancelText: {
				type: String,
				default: '取消'
			},
			confirmText: {
				type: String,
				default: '确定'
			}
		},
		data() {
			return {
				
			};
		},
		methods: {
			close() {
				this.$emit("update:visible", false)
			},
			onCancel() {
				this.$emit("cancel")
				this.close()
			},
			onConfirm() {
				this.$emit("confirm")
				this.close()
			}
		}
	}
</script>

<style lang="scss" scoped>
.custom-modal {
	&_inner {
		width: calc(100vw - 40rpx);
		padding: 120rpx 30rpx 42rpx;
		box-sizing: border-box;
		@include bgUrl("order_bg_title.png");
		.title {
			font-weight: bold;
			font-size: 28rpx;
			text-align: center;
			line-height: 1;
			height: 100rpx;
		}
		.bottom-bar {
			display: flex;
			justify-content: flex-end;
			gap: 30rpx;
			padding-top: 22rpx;
			height: 56rpx;
		}
	}
}
</style>