<template>
	<custom-page bg="notice-bg.png">
	    <custom-header title="核销结果" bg-color="transparent"></custom-header>
		<view class="flex">
			<view :class="info.status=='核销成功' ? 'success' : 'fail'  "></view>
			<view class="status">{{info.status}}</view>
			<view class="text"> <text style="color: #B36859;">{{times}}</text> s后自动返回门票核销首页</view>
		</view>
		
		<custom-button class="menu" @tap="continues">继续核销</custom-button>
	</custom-page>
</template>
<script>
    import {writeCode} from "@/api/my.js"
	export default {
		data() {
			return {
			  info:{
				  status:'核销成功'
			  },
			  times:3,
			  stop:null
			};
		},
		onLoad(opt){
			if(opt&&opt.status=='核销成功'){
				this.info.status = '核销成功'
			}else if(opt&&opt.status=='核销失败'){
				this.info.status = '核销失败'
			}
			let timer = setInterval(() => {
				this.times -= 1;
				if (this.times <= 0) {
				  clearInterval(timer); 
				  /* uni.redirectTo({
					url:'/pages/pages-my/writeOff/index'
				  }); */
				  uni.navigateBack()
				}
			}, 1000);
			this.stop  = timer
		},
		methods:{
			continues(){
				 clearInterval(this.stop); 
				 uni.navigateBack();
				 //安卓环境触发扫码ios返回上一页
				 if (uni.getSystemInfoSync().platform == 'android') {
				 	 this.scanCode()
				 }
			},
			//扫一扫
			scanCode() {
				// 允许从相机和相册扫码
				uni.scanCode({
					// scanType: ['QR_CODE'], //条形码
					success: function(res) {
						// ('条码类型：' + res.scanType);
						// ('条码内容：' + res.result);
						// 微信小程序
						if (res.errMsg == "scanCode:ok") {
							// 扫描到的信息
                            let params = {
                            	res: res.result
                            }
							writeCode(params).then((res)=>{
								this.datas = res
								uni.redirectTo({
									url:'/pages/pages-my/writeOff/details?datas='+ encodeURIComponent(JSON.stringify(this.datas))
								})
							})
						}
					}
				});
			}
		}
	}
</script>
<style lang="scss" scoped>
.menu{
	width: 100%;
	display: flex;
	justify-content: center;
	position: fixed;
	bottom: 80rpx;
}
.flex{
	display: flex;
	flex-direction: column;
	align-items: center;
	overflow: hidden;
	.success{
		width: 150rpx;
		height: 150rpx;
	    margin-top: 140rpx;
		margin-bottom: 78rpx;
		@include bgUrl('writeOff-success.png');
		background-size: 100% 100%;
	}
	.fail{
		width: 150rpx;
		height: 150rpx;
	    margin-top: 140rpx;
		margin-bottom: 78rpx;
		@include bgUrl('writeOff-fail.png');
		background-size: 100% 100%;
	}
	.status{
		margin-bottom: 33rpx;
		font-weight: 400;
		font-size: 34rpx;
		color: $main-font-color;
	}
	.text{
		font-weight: 400;
		font-size: 28rpx;
		color:$tip-font-color;
	}
}
</style>


