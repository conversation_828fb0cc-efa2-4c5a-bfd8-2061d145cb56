<template>
	<custom-page :bg-top="navHeight" :empty="feedlist.length === 0">
		<custom-header title="意见反馈" :navheight.sync="navHeight" show-loading></custom-header>
		<u-loading-icon class="loading"  :show="show" text="加载中" textSize="18"></u-loading-icon>
		<view v-if="show==false">
			<!-- 空数据 -->
			<!-- <view v-if="feedlist==''" class="null"></view>
			<view v-if="feedlist==''" class="null-text">暂无数据</view> -->
			<!-- 有数据 -->
			<view v-if="feedlist!=''" class="feedlist">
				<view class="scroll">   				    
					<view class="feedlists" v-for="(item,index) in feedlist" @click="details(item)">
						<view class="feedlist-box" :style="{marginBottom: item.state=='1'? '0rpx':'20rpx'}" >
							<view class="feedbox-title">
								<view class="title-l">内容描述</view>
								<view :class="item.type=='2'?'title-r':'title-r-two'">{{item.typeName}}</view>
							</view>
							<view class="feedbox-content">
								{{item.content}}
							</view>
							<view class="feedbox-time">
								{{item.createTime}}
							</view>	
						</view>
						
						<view v-if="item.state=='1'" class="feedlist-boxT">
							<view class="feedlist-line"></view>
							<view class="feedlist-lineT"></view>
							<view class="boxT">
								<view class="boxT-text">回复</view>
								<view class="boxT-content">{{item.replyContent}}</view>
								<view class="boxT-time">{{item.replyTime}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 底部蒙层 -->
			<view class="opacity"></view>
			<custom-button class="menu" @tap="feedback">去反馈</custom-button>
		</view>
	</custom-page>
</template>

<script>
	import {feedbackList} from "@/api/my.js"
	export default {
		data() {
			return {
				show:true,
	            navHeight:0,
				feedlist:[],
				pageNum:'1',
				pageSize:'10',
				total:'0',
			};
		},
		created(){
			this.show = false
			this.getlist()
		},
		onShow(){

		},
		onReachBottom(){
			if(this.pageNum*this.pageSize<this.total){
				this.pageNum++
				this.getlist()
			}
		},
		onPullDownRefresh(v){
			this.pullDown()		
		},
		methods:{
			async getlist(){
				  let data = {
					  pageNum:this.pageNum,
					  pageSize:this.pageSize
				  }
				  const { rows,total } = await feedbackList(data);
				  this.total = total
				  if(total <= '10'){
					  this.feedlist = rows
				  }else if(total > '10'){
					  this.feedlist = [...this.feedlist,...rows]
					  console.log(this.feedlist,'this.feedlist');
				  } 
			},
			pullDown(){
				this.pageNum = '1'
				this.feedlist = []
				this.getlist()
				setTimeout(()=>{
					uni.stopPullDownRefresh()
				},400)
			},
			details(item){
				uni.navigateTo({
					url:'/pages/pages-my/feedback/details?item='+ encodeURIComponent(JSON.stringify(item))
				})
			},
			feedback(){
				uni.navigateTo({
					url:'/pages/pages-my/feedback/feedback'
				})
			}
		}
	}
</script>

<style lang="scss">
.feedlist{
	width: calc(100% - 40rpx);
	margin: 20rpx;
	.scroll{
		margin-bottom: 300rpx;
		.feedlists{
			z-index: 99999;
			.feedlist-box{
				width: 100%;
				height: 200rpx;
				@include bgUrl('feedback-box.png');
				background-size: 100% 100%;
				position: relative;
				overflow: hidden;
				.feedbox-title{
					width: calc(100% - 68rpx);
					height: 32rpx;
					line-height: 32rpx;
					margin-left: 34rpx;
					margin-top: 37rpx;
					margin-bottom: 17rpx;
					display: flex;
					align-items: center;
					justify-content:space-between;
					.title-l{
						font-weight: 400;
						font-size: 24rpx;
						color: $tip-font-color;
						font-style: normal;
					}
					.title-r{
						width: 80rpx;
						height: 32rpx;
						background: $sec-font-color;
						border-radius: 2rpx 2rpx 2rpx 2rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						font-weight: 400;
						font-size: 22rpx;
						color: #FFFFFF;
						font-style: normal;
					}
					.title-r-two{
						width: 80rpx;
						height: 32rpx;
						background: #7B9D7C;
						border-radius: 2rpx 2rpx 2rpx 2rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						font-weight: 400;
						font-size: 22rpx;
						color: #FFFFFF;
						font-style: normal;
					}
				}
			    .feedbox-content{
					width: calc(100% - 68rpx);
					height: 30rpx;
					line-height: 30rpx;
					margin-left: 34rpx;
					white-space: nowrap;    
					overflow: hidden;        
					text-overflow: ellipsis; 
					margin-bottom: 26rpx;
				}
				.feedbox-time{
					margin-left: 34rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: $tip-font-color;
					font-style: normal;
				}
			}
			.feedlist-boxT{
				width: 100%;
				height: 190rpx;
				position: relative;
				.feedlist-line{
					width: 10rpx;
					height: 50rpx;
					position: absolute;
					top: -30rpx;
					left: 45rpx;
					@include bgUrl('feedback-line.png');
					background-size: 100% 100% !important;
					z-index: 999;
				}
				.feedlist-lineT{
					width: 10rpx;
					height: 50rpx;
					position: absolute;
					top: -30rpx;
					right: 45rpx;
					@include bgUrl('feedback-line.png');
					background-size: 100% 100% !important;
					z-index: 999;
				}
				.boxT{
					width: 100%;
					height: 180rpx;
					margin-top: 10rpx;
					overflow: hidden;
					background: linear-gradient(#faf5eb, #fffdfa);
				
					
					box-sizing: border-box;
					border: 1px solid;
					border-image: radial-gradient(circle, #f2ece2, #d0c9bc);
					border-image-slice: 1;
				
					.boxT-text{
						height: 23rpx;
						line-height: 23rpx;
						margin-top: 30rpx;
						margin-bottom: 22rpx;
						margin-left: 31rpx;
						font-size: 24rpx;
						color: $tip-font-color;
					}
					.boxT-content{
						width: calc(100% - 68rpx);
						height: 30rpx;
						line-height: 30rpx;
						margin-left: 34rpx;
						white-space: nowrap;    
						overflow: hidden;        
						text-overflow: ellipsis; 
						margin-bottom: 27rpx;
					}
					.boxT-time{
						margin-left: 34rpx;
						height: 16rpx;
						line-height: 16rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: $tip-font-color;
						font-style: normal;
					}
				}
			}
		}
	}
}	
//空数据
.null{
	width: 600rpx;
	height: 600rpx;
	@include bgUrl('feedback-none.png.png');
	background-size: 100% 100% !important;
	margin: 0 auto;
	margin-top: 200rpx;
}
.null-text{
	width: 100%;
	text-align: center;
	margin-top: 30rpx;
	font-weight: 400;
	font-size: 28rpx;
	color: $main-font-color;
}
//底部按钮
.menu{
	width: 220rpx;
	height: 130rpx;
	position: fixed;
	bottom: 0rpx;
	left: 50%;
	right: 50%;
	transform: translate(-50%,-50%);
	text-align: center;
	overflow: hidden;
	z-index: 999;
}
//底部蒙层
.opacity{
	width: 100%;
	height: 300rpx;
	position: fixed;
	bottom: -200rpx;
	left: 50%;
	right: 50%;
	transform: translate(-50%,-50%);
	opacity: 0.4;
	background-color: #f7f7f2;
	z-index: 998;
}
// 加载动画
::v-deep .u-loading-icon{
	height: 80vh;
}
</style>
