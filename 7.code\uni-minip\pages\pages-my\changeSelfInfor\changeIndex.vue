<template>
    <custom-page :bg-top="navHeight">
        <custom-header :title="headerTitle" :navheight.sync="navHeight"></custom-header>
        <view class="topLine"></view>
        <view class="box">
            <view class="content">
                <view class="box-num">
                    <view class="num">
                        <text class="boxType-text">*</text>
                        <view class="num-r">手机号</view>
                    </view>
                    <input class="input" v-model="info.phonenumber" type="number" placeholder="请填写" />
                </view>
                <view class="box-num">
                    <view class="num">
                        <text class="boxType-text">*</text>
                        <view class="num-r">验证码</view>
                    </view>
                    <input class="input" v-model="info.smsCode" type="number" placeholder="请填写" />
                    <view class="get-phone" @click="getCodeFun">{{tips}}</view>
                </view>
            </view>
        </view>
        <view class="bottomLine"></view>
        <view class="calendar-footer">
            <custom-button type="primary" block @click="customConfirm">验证后绑定新手机</custom-button>
        </view>
    </custom-page>
</template>

<script>
import { getCodePhone, editIphone } from "@/api/my.js";
import picker from '@/components/custom-picker/picker.vue';
import config from '@/utils/config.js';
export default {
    components: {
        picker
    },
    data () {
        return {
            headerTitle: '换绑手机',//页面标题
            info: {
                phonenumber: "", //手机号
                smsCode: "", //验证码
            },
            tips: '获取验证码',
            disabled: false,
            seconds: 60,
            timer: null,

            navHeight: 0,
            redisable: false,
            files: [],
            label: '请选择',
            value: '',
            picklist: [
                {
                    label: '身份证',
                    value: '1'
                },
                // {
                //     label: '投诉',
                //     value: '2'
                // }
            ],
            agreeStatus: false,
            isUpdate: false,//是否编辑

        };
    },
    methods: {
        // 获取验证码
        getCodeFun () {
            if (!this.disabled) {
                let params = {
                    phonenumber: this.info.phonenumber
                };
                this.tips = '正在发送...';
                getCodePhone(params).then((res) => {
                    if (res.code == 200) {
                        this.countdown();
                    } else {
                        console.log('发送失败');
                        // uni.$u.toast(res.msg);
                        this.tips = '获取验证码';
                    }
                }).catch(() => {
					this.tips = '获取验证码';
				});
            }
        },
        // 倒计时
        countdown () {
            clearInterval(this.timer);
            this.seconds = 60;
            this.disabled = true;
            this.tips = this.seconds + 's';
            this.timer = setInterval(() => {
                this.seconds--;
                this.tips = this.seconds + 's';
            }, 1000);
        },
        // 绑定新手机
        customConfirm () {
            let params = {
                phone: this.info.phonenumber,
                smsCode: this.info.smsCode,
            };
			
            editIphone(params).then((res) => {
                if (res) {
                    uni.showToast({
                        title: '修改成功',
                        icon: 'none'
                    });
					this.$store.dispatch('GET_USER_INFO')
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 600);
                } else {
                    uni.$u.toast(res.msg);

                }
            });
        },
    },
    onLoad (OPTION) {

    },
    onShow () {

    }
}
</script>

<style lang="scss">
.topLine {
    width: calc(100% - 20rpx);
    height: 26rpx;
    margin-left: 10rpx;
    @include bgUrl('feedback-top.png');
    background-size: 100% 100% !important;
}
.bottomLine {
    width: calc(100% - 20rpx);
    height: 26rpx;
    margin-top: -4rpx;
    margin-left: 10rpx;
    @include bgUrl('feedback-bottom.png');
    background-size: 100% 100% !important;
}
.calendar-footer {
    padding: 10rpx 20rpx 0;
    margin-top: 30rpx;
}

.boxType-text {
    width: 12rpx;
    height: 12rpx;
    margin-right: 10rpx;
    color: $sec-font-color;
    font-weight: 400;
    margin-bottom: 14rpx;
}
.protocal-text {
    font-weight: 400;
    font-size: 24rpx;
    color: $tip-font-color;
}
.box-bottom-text {
    width: calc(100% - 60rpx);
    margin: 0 30rpx;
    .argee-text {
        font-weight: 400;
        font-size: 24rpx;
        color: $sec-font-color;
    }
}

.box-bottom {
    width: calc(100% - 60rpx);
    margin-left: 30rpx;
    margin-top: 37rpx;
    .custom-check {
        width: 28rpx;
        height: 28rpx;
        @include bgUrl('icon_checkbox_inchecked.png');
        margin-right: 10rpx;
        margin-top: 6rpx;
    }
    label {
        display: flex;
    }
    checkbox {
        display: none;
        &[checked] {
            & + .custom-check {
                @include bgUrl('icon_checkbox_checked.png');
                pointer-events: none;
            }
        }
    }
}

.box {
    width: calc(100% - 50rpx);
    margin-top: -4rpx;
    margin-left: 25rpx;
    background: #e8e2d4;
    overflow: hidden;
    .content {
        margin: 10rpx;
        background: #ffffff;
        overflow: hidden;
        .box-type {
            width: calc(100% - 40rpx);
            height: 100rpx;
            margin-left: 20rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1rpx solid #d0c9bc;
            box-sizing: border-box;
            .boxType-l {
                height: 26rpx;
                line-height: 26rpx;
                display: flex;
                align-items: center;

                .boxType-texts {
                    height: 26rpx;
                    line-height: 26rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                }
            }
            .boxType-r {
                height: 50rpx;
                line-height: 50rpx;
                display: flex;
                align-items: center;
                .boxTypeR-text {
                    height: 30rpx;
                    line-height: 30rpx;
                    font-size: 28rpx;
                    color: $tip-font-color;
                    font-weight: 400;
                    margin-right: 10rpx;
                }
                .boxTypeR-img {
                    width: 50rpx;
                    height: 50rpx;
                    line-height: 50rpx;
                    @include bgUrl('feedback-return.png');
                    background-size: 100% 100% !important;
                }
            }
        }

        .box-num {
            width: calc(100% - 40rpx);
            height: 100rpx;
            margin-left: 20rpx;
            border-bottom: 1rpx solid #d0c9bc;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 20rpx;
            .num {
                height: 30rpx;
                line-height: 30rpx;
                width: 135rpx;
                display: flex;
                align-items: center;
                .num-l {
                    font-weight: bold;
                    font-size: 28rpx;
                    color: $sec-font-color;
                    margin-right: 12rpx;
                }
                .num-r {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #1a1818;
                }
            }
            .input {
                text-align: left;
                vertical-align: center;
                margin-right: 55rpx;
                font-size: 28rpx;
                width: 50%;
                flex: 1;
                height: 50rpx;
                font-size: 28rpx;
                color: $tip-font-color;
                line-height: 40rpx;
                margin-left: 20rpx;
                font-family: 'songti' !important;
            }
            .get-phone {
                min-width: 150rpx;
                font-size: 28rpx;
                color: #b36859;
                text-align: end;
                margin-right: 10rpx;
            }
        }
    }
}
.flex_jus_sb {
    justify-content: flex-end;
}
</style>
