import { IMG_URL } from "@/utils/config"
export default {
	palette: {
		"width": "672rpx",
		"height": "994rpx",
		"views": [
			{
				"id": "mainImage",
				"type": "image",
				"url": "",
				"css": {
					"width": "479rpx",
					"height": "720rpx",
					"top": "84rpx",
					"left": "96rpx"
				}
			},
			{
				"type": "image",
				"url": IMG_URL + "tshj_share_poster_bg.png",
				"css": {
					"width": "672rpx",
					"height": "994rpx",
					"top": "0",
					"left": "0"
				}
			},
			{
				"id": "petName",
				"type": "text",
				"text": "前往辽宁省博物馆二层西侧,\n唐宋风华数字展厅,\n解锁同款古风图片。",
				"css": {
					"width": "265rpx",
					"top": "830rpx",
					"left": "94rpx",
					"fontWeight": "bold",
					"fontSize": "20rpx",
					"color": "#121212",
					"lineHeight": "28rpx",
					"fontFamily": "songti"
				}
			},
			{
				"type": "rect",
				"css": {
					"width": "1rpx",
					"height": "80rpx",
					"right": "315rpx",
					"bottom": "82rpx",
					"color": "#4A3006"
				}
			},
			{
				"type": "image",
				"url": IMG_URL + "museum_miniapp_qrcode.png",
				"css": {
					"width": "83rpx",
					"height": "83rpx",
					"right": "210rpx",
					"bottom": "82rpx",
					"borderRadius": "10rpx"
				}
			},
			{
				"id": "qrcodeTip",
				"type": "text",
				"text": "长按二维码\n识别",
				"css": {
					"width": "110rpx",
					"right": "90rpx",
					"bottom": "94rpx",
					"fontWeight": "bold",
					"fontSize": "20rpx",
					"color": "#121212",
					"lineHeight": "28rpx",
					"fontFamily": "songti"
				}
			}
		]
	},
	setValues(values) {
		/* 
		 
		 this.palette = poster.setValues({
		 	meetTheStandard: false, // 是否达标
		 	petName: '元宝',
		 	// exercise: '已达标',
		 	petAvatar: 'http://sdmp-petc-img.jinhuaze.com/upload/20240425/ebf0beadf81b4e6ca2145ae4e494b927.jpeg',
		 	medalsName: '无敌跑王',
		 	medalsImg: 'http://sdmp-petc-img.jinhuaze.com/app/c-app/medals.png',
		 	beyondQuantity: "998",
		 	pokemonText: "今日狗狗运动虽然比较充足，但是消耗热量还远远不够，还需要继续加油哦！今日狗狗运动虽然比较充足但是还需要继续加油哦！",
		 	qrcode: "你长得真好看"
		 })
		 
		 */
		Object.entries(values).forEach(([id, value]) => {
			const findIndex = this.palette.views.findIndex(f => f.id === id);
			if (findIndex > -1) {
				const view = this.palette.views[findIndex];
				switch (true) {
					case view.hasOwnProperty('text'):
						view.text = value;
						break;
					case view.hasOwnProperty('url'):
						view.url = value;
						break;
					case view.hasOwnProperty('content'):
						view.content = value;
						break;
				}
			}
		});
		return this.palette
	}

}