// 这个文件写通用的工具方法。
import store from "@/store/index.js"
import {
	payOrder,
	prepayByBestPay
} from "@/api/order"

export function testUtil() {
	return 'abcdefg'
}

// 检查是否登录，未登录跳转到登录页，返回一个布尔值，可以做进一步逻辑判断。
export function checkLogin() {
	if (!store.state.token) {
		uni.navigateTo({
			url: '/pages/login/login'
		})
		return false
	}
	return true
}

// 获取当前月的最后一天
export function getMonthLastDate() {
	const d = new Date()
	const year = d.getFullYear()
	let month = d.getMonth() + 1
	month = month < 10 ? `0${month}` : month
	const date = d.getDate()
	d.setMonth(month);
	d.setDate(0);
	return `${year}-${month}-${d.getDate()}`
}
// 加密身份证
export function encryptIdCard(value) {
	if (!value || value.length !== 18) return value;
	return value.substring(0, 3) + '***********' + value.substring(14);
}
// 加密手机号
export function encryptPhone(value) {
	if (!value || value.length !== 11) return value;
	return value.substring(0, 3) + '****' + value.substring(7);
}

export async function payment(orderSn, preFn = prepayByBestPay) {
	uni.showLoading({
		title: '请稍后...'
	})
	return new Promise(async (resolve, reject) => {
		// const {
		// 	nonceStr,
		// 	paySign,
		// 	packageValue,
		// 	timeStamp,
		// 	signType
		// } = await payOrder({
		// 	orderSn: orderSn,
		// 	payType: 1
		// })
		const payResult = await preFn({
			orderSn: orderSn,
			payType: 1
		})
		console.log(payResult, 'payResult');
		uni.hideLoading();
		wx.openEmbeddedMiniProgram({
			appId: payResult.appId, //跳转的小程序的appId
			path: payResult.path, //如果这里不填，默认是跳转到对方小程序的主页面
			envVersion: 'release',
			// release: 正式版  trial: 体验版
			success(res) {
				// 打开成功
				
				console.log("支付成功", res);
				resolve(res)
			},
			fail(error) {
				console.log("支付失败", error)
			},
			complete(com) {
			}
		})
		// wx.requestPayment({
		// 	timeStamp,
		// 	package: packageValue,
		// 	nonceStr,
		// 	paySign,
		// 	signType,
		// 	success: (res) => {
		// 		console.log("支付成功", res)
		// 		resolve(res)
		// 	},
		// 	fail (res) {
		// 		console.log('支付失败', res)
		// 		// reject(res)
		// 	}
		// })
	})
}