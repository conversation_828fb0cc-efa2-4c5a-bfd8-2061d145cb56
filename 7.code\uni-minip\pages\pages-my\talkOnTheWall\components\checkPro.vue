<template>
    <view class="custom-modal" :catchtouchmove="true">
        <u-popup :show="visible" round="6rpx" mode="center" :safeAreaInsetBottom="false" overlayOpacity="0.4"
            :negative-top="200" :closeOnClickOverlay="false" @close="close">
            <view class="custom-modal_inner">
                <image mode='widthFix' class="bg-img" :src="imgUrl"></image>
            </view>
            <custom-button class="menu" @tap="visible = false">{{ cancelText }}</custom-button>
        </u-popup>
    </view>
</template>

<script>
import { getNotice } from "@/api/ticket.js";

export default {

    name: "custom-modal",
    props: {
        cancelText: {
            type: String,
            default: '关闭'
        },
        imgUrl: {
            type: String,
        },
    },
    mounted() {
        // this.init();
    },
    data() {
        return {
            visible: false,
            scrollTop: 0
        };
    },
    methods: {
        close() {
            this.visible = false;
        },
        showPop() {
            this.visible = true;
        },
        onCancel() {
            this.$emit("cancel");
            this.close();
        },
        onConfirm() {
            this.$emit("confirm");
            this.close();
        },
        // 获取我的作品
        init() {
            getNotice().then((res) => {
                if (res) {
                    this.info = res;
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-popup__content {
    position: relative;
}

.menu {
    width: 220rpx;
    height: 130rpx;
    position: absolute;
    bottom: -248rpx;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.recomm-scroll {
    height: calc(40vh);
}

.custom-modal {
    &_inner {
        width: calc(100vw - 70rpx);
        // height: 60vh;
        padding: 13rpx;
        box-sizing: border-box;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;

        .bg-img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>