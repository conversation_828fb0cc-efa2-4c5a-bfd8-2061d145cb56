<template>
	<custom-page :bg-top="navHeight">
		<custom-header title="意见反馈" :navheight.sync="navHeight"></custom-header>
		<view class="topLine"></view>
		<view class="box">
			<view class="content">
				<view class="describe">
					<view class="text">内容描述</view>
					<view :class="info.type=='2'?'label':'labelT'">{{info.typeName}}</view>
				</view>
				<view class="body">
					{{info.content}}
				</view>
				<view class="image">
					<image @click="widFix(UPLOAD_IMG_URL+item)"  class="image-flex" v-for="(item,index) in info.imgList" :src="UPLOAD_IMG_URL+item" mode="aspectFill"></image>
				</view>
				<view class="time">{{info.createTime}}</view>
				<view v-if="info.state=='1'">
					<view class="line"></view>
					<view class="reply">回复</view>
					<view class="body mb34">{{info.replyContent}}</view>
					<view class="time">{{info.replyTime}}</view>
				</view>
			</view>
		</view>
		<view class="bottomLine"></view>
		<u-overlay :show="show" @click="show = false">
			<image class="overlayImg" :src="urls" mode="widthFix" :style="{ height: imgHeight + 'px' }" @load="onImgLoad" ></image>
		</u-overlay>
	</custom-page>
</template>

<script>
	import {UPLOAD_IMG_URL} from "@/utils/config.js"
	export default {
		data() {
			return {
				UPLOAD_IMG_URL,
				info:{},
				urls:'',
				imgHeight:0,
				navHeight:0,
				show: false
			};
		},
		methods:{
			widFix(url){
				if(url){
					this.show = true
					this.urls = url
				}		
			},
			onImgLoad(e) {
			  // 当图片加载完成后，获取图片的原始宽度和高度，并根据宽度计算出高度
			  const { width, height } = e.mp.detail;
			  this.imgHeight = (height / width) * 100; // 高度 = 原始高度 / 原始宽度 * 100
			}
		},
		onLoad(option){
			if(option.item){ 
				this.info = JSON.parse(decodeURIComponent(option.item))
			}
		}
	}
</script>

<style lang="scss">
.topLine{
	width: calc(100% - 20rpx);
	height: 26rpx;
	margin-left: 10rpx;
	@include bgUrl('feedback-top.png');
	background-size: 100% 100% !important;
}
.bottomLine{
	width: calc(100% - 20rpx);
	height: 26rpx;
	margin-top: -4rpx;
	margin-left: 10rpx;
	@include bgUrl('feedback-bottom.png');
	background-size: 100% 100% !important;
}
.overlayImg{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	margin: auto;
	width: 100%;
}
.box{
	width: calc(100% - 50rpx);
	margin-top: -4rpx;
	margin-left: 25rpx;
	background: #E8E2D4;
	overflow: hidden;
	.content{
		margin: 10rpx;
		background: #FFFFFF;
		overflow: hidden;
		.describe{
			width: calc(100% - 40rpx);
			height: 32rpx;
			margin: 27rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.text{
				font-weight: 400;
				font-size: 24rpx;
				color: $tip-font-color;
				font-style: normal;
			}
			.label{
				width: 80rpx;
				height: 32rpx;
				line-height: 32rpx;
				background: $sec-font-color;
				border-radius: 2rpx 2rpx 2rpx 2rpx;
				box-sizing: border-box;
				text-align: center;
				font-weight: 400;
				font-size: 22rpx;
				color: #FFFFFF;
				font-style: normal;
			}
			.labelT{
				width: 80rpx;
				height: 32rpx;
				line-height: 32rpx;
				background: #7B9D7C;
				border-radius: 2rpx 2rpx 2rpx 2rpx;
				box-sizing: border-box;
				text-align: center;
				font-weight: 400;
				font-size: 22rpx;
				color: #FFFFFF;
				font-style: normal;
			}
		}
		.body{
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			margin-bottom: 30rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #1A1818;
			font-style: normal;
		}
		.mb34{
			margin-bottom: 34rpx !important;
		}
		.image{
			width: calc(100% - 30rpx);
			margin-left: 20rpx;
			margin-right: 10rpx;
			margin-bottom: 30rpx;
			display: flex;
			.image-flex{
				width: 120rpx;
				height: 120rpx;
				margin-right: 10rpx;
			}
		}
		.time{
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			height: 16rpx;
			line-height: 16rpx;
			margin-bottom: 40rpx;
			font-weight: 400;
			font-size: 22rpx;
			color: $tip-font-color;
			font-style: normal;
		}
		.line{
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			margin-bottom: 30rpx;
			height: 1rpx;
			background: #D0C9BC;
		}
		.reply{
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			margin-bottom: 32rpx;
			font-size: 24rpx;
			color: $tip-font-color;
		}
	}
}

</style>
