<template>
	<view class="custom-swipe-action">
		<view v-for="(item, index) in data" :key="item.id">
			<view class="custom-swipe-action-item">
				<view class="swipe-action">
					<view class="swipe-action__content">
						<view class="del-icon" :class="{active: item.show}" @click="toggleOpen(item, index)"></view>
						<slot :row="item"></slot>
					</view>
					
					<view class="right"
					 :style="{height: itemHeight + 'px', paddingLeft: item.show ? '10rpx' : '0', width: showRight && item.show ? buttonWidth - 10 + 'rpx': '0'}"
					 @click="onRightClick(item, index)"
					 >
						<slot name="right">删除</slot>
					</view>
				</view>
			</view>
			<view v-if="index < data.length - 1" class="divider"></view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "custom-swipe-action",
		props: {
			data: Array,
			showDivider: Boolean,
			buttonWidth: {
				type: Number,
				default: 106
			}
		},
		data() {
			return {
				itemHeight: 0,
				showRight: false
			}
		},
		mounted() {
			this.queryRect()
		},
		methods: {
			queryRect() {
				this.$uGetRect('.custom-swipe-action-item', true).then(rect => {
					this.itemHeight = rect[0].height
					setTimeout(() => {
						this.showRight = true
					}, 100)
				})
			},
			toggleOpen(item, index) {
				this.$emit('toggle', {
					show: item.show,
					index
				})
			},
			onRightClick(item, index) {
				this.$emit("rightClick", {
					...item,
					index
				})
			}
		},
		options: {
			styleIsolation: "shared"
		}
	}
</script>

<style lang="scss" scoped>
	.custom-swipe-action {
		// padding: 0 33rpx;
		.swipe-action {
			display: flex;
			background-color: #fff;
		}
		.swipe-action__content {
			flex: 1;
			display: flex;
			gap: 33rpx;
			align-items: center;
			background-color: #fff;
			padding: 0 33rpx 0 13rpx;
			border-radius: 0 6rpx 6rpx 0;
			transform: translateX(10rpx);
		}
		.del-icon {
			// border: 1rpx solid red;
			width: 54rpx;
			height: 54rpx;
			@include bgUrl("line_icon_delete.png");
			background-size: 34rpx;
			background-position: center;
			transition: all .2s;
			&.active {
				transform: rotate(90deg);
			}
		}
		.right {
			height: 100%;
			background: $sec-font-color;
			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all .2s;
			white-space: nowrap;
			padding-left: 10rpx;
		}
	}
	
	
</style>