<template>
    <custom-page :safe-area-inset-bottom="false">
        <custom-header bg-color="transparent" :placeholder="false" :navheight.sync="navHeight"
            :show-back-btn="!isGbln"></custom-header>
        <view class="login-page-container" :style="{paddingTop: `calc(172rpx + ${navHeight}px)`}">
            <image class="logo" :src="isGbln ? 'https://museum-img.jinhuaze.com/guobao/gbln_logo.png' : logo" mode="aspectFill"></image>

            <view class="title">
                <view v-if="!isGbln">辽宁省博物馆-数字展厅票务</view>
                <view v-else>辽宁省博物馆-国宝辽宁·精选版</view>
                <text v-if="!isGbln">Digital Art</text>
            </view>

            <custom-button size="large" :disabled="!agreeStatus" :open-type="openType" @getphonenumber="getPhoneNumber"
                @click="onLoginClick">授权登录</custom-button>

            <view class="protocol-view">
                <checkbox-group @change="onCheckboxChange">
                    <label>
                        <checkbox :value='1' :checked="agreeStatus"></checkbox>
                        <view class="custom-check"></view>
                        <view class="protocal-text">我已阅读并同意<text @click.stop="toPage(1)">《服务协议》</text>、<text
                                @click.stop="toPage(2)">《隐私协议》</text>、<text @click.stop="toPage(3)">《儿童隐私协议》</text> 及
                            <text @click.stop="toPage(4)">《个人信息与第三方共享清单》</text> 的所有条款
                        </view>
                    </label>
                </checkbox-group>
            </view>
        </view>
    </custom-page>
</template>

<script>
import { IMG_URL } from '@/utils/config';
import { mapState } from 'vuex';
import { checkPhone } from '@/api/login';

export default {
    data () {
        return {
            IMG_URL,
            agreeStatus: false,
            navHeight: 0,
            openType: 'getPhoneNumber'
        };
    },
    computed: {
        ...mapState(['logo']),
        isGbln () {
            return uni.getStorageSync('guobaoln')
        },
        isAiFace () {
            return uni.getStorageSync('aiFace')
        }
    },
    onLoad () {
        this.checkPhone();
    },
    methods: {
        async checkPhone () {
            wx.login({
                success: async ({ code }) => {
                    const { phone } = await checkPhone({ code });
                    console.log(phone);
                    if (phone) {
                        this.openType = '';
                    }
                }
            });
        },
        onCheckboxChange (e) {
            this.agreeStatus = e.detail.value.length > 0;
        },
        async getPhoneNumber (e) {
            if (!this.agreeStatus || !e.detail.code) return;
            this.handleLogin(e.detail.code);
        },
        onLoginClick () {
            if (this.openType) return;
            console.log('LOGIN', this.openType);
            // 直接登录，不用调用手机号。
            this.handleLogin(null);
        },
        async handleLogin (phoneCode) {
            try {
                await this.$store.dispatch('LOGIN', phoneCode);
                if (this.isGbln) {
                    uni.reLaunch({
                        url: '/pages/pages-gbln/ticketBooking/ticketBooking'
                    });
                    return
                }
                if (this.isAiFace) {
                    uni.reLaunch({
                        url: '/pages/pages-my/tangsongDynastyPaintings/index'
                    });
                    return
                }
                if (uni.getStorageSync("scene") == "partyCode") {
                    uni.navigateTo({
                        url: '/pages/pages-index/party/index'
                    });
                } else if (uni.getStorageSync("sceneMy")) {
                    // 扫码进入我的作品页面
                    uni.reLaunch({
                        url: '/pages/pages-my/myWork/index'
                    });
                }
                else {
                    uni.switchTab({
                        url: '/pages/pages-tabs/home/<USER>'
                    });
                }
            } catch {
                console.log("登录失败");
            }
        },
        toPage (type) {
            console.log("点击协议");
            uni.navigateTo({
                url: '/pages/pages-my/protocol/index?type=' + type
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.login-page-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 172rpx;
    box-sizing: border-box;

    @include bgUrl('login_bg.png');
    background-size: 100% auto;
    min-height: 100vh;
    .logo {
        width: 140rpx;
        height: 140rpx;
        border-radius: 50%;
        background: #e6e1d2;
        border-radius: 50%;
        border: 2px solid $border-color;
    }
    .title {
        text-align: center;
        margin-bottom: 350rpx;
        view {
            font-weight: 500;
            font-size: 40rpx;
            line-height: 48rpx;
            padding: 60rpx 0 30rpx;
        }
        text {
            text-transform: uppercase;
            font-weight: 400;
            font-size: 18rpx;
            color: #4d3b2e;
            letter-spacing: 10rpx;
        }
    }
    .protocol-view {
        text-align: left;
        width: 100%;
        padding: 122rpx 46rpx 0;
        box-sizing: border-box;
        font-weight: 400;
        font-size: 24rpx;
        color: $tip-font-color;
        .protocal-text {
            flex: 1;
            text {
                color: $sec-font-color;
                font-weight: bold;
            }
        }
        .custom-check {
            width: 28rpx;
            height: 28rpx;
            @include bgUrl('icon_checkbox_inchecked.png');
            margin-right: 10rpx;
            margin-top: 6rpx;
        }
        label {
            display: flex;
        }
        checkbox {
            display: none;
            &[checked] {
                & + .custom-check {
                    @include bgUrl('icon_checkbox_checked.png');
                    pointer-events: none;
                }
            }
        }
    }
}
</style>