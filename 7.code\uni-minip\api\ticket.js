import { request } from '@/utils/request.js';

// 获取购票须知、展厅介绍
export const getNotice = () => {
    return request({
        url: 'museum-app/app/digitalShowroomManage/detail',
        method: 'get',
    });
};

// /museum-app/app/digitalShowroomManage/info
export const getTicketBookingInfo = () => {
    return request({
        url: 'museum-app/app/digitalShowroomManage/info',
        method: 'get',
    });
};

export const checkOrder = (data) => {
    return request({
        url: 'museum-app/order/checkOrder',
        method: 'post',
		data
    });
};
// 提交订单  museum-app/order/submit
export const submitOrder = (data) => {
    return request({
        url: 'museum-app/order/submit',
        method: 'post',
		data
    });
};

