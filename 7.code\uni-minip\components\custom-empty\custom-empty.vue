<template>
	<view class="custom-empty" :style="{marginTop: top}">
		<view class="icon_bg" :style="{transform: `scale(${zoom})`}"></view>
		<text class="tip-text">{{ text || '暂无数据' }}</text>
	</view>
</template>

<script>
	export default {
		name:"custom-empty",
		props: {
			top: {
				type: String,
				default: '300rpx'
			},
			zoom: {
				type: Number,
				default: 1
			},
			text: {
				type: String,
				default: '暂无数据'
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="scss" scoped>
	.custom-empty {
		text-align: center;
		.icon_bg {
			margin: 0 auto;
			width: 500rpx;
			height: 400rpx;
			@include bgUrl("empty_bg.png");
		}
		.tip-text {
			font-weight: 400;
			font-size: 28rpx;
			margin-top: 30rpx;
		}
	}
</style>