<template>
    <view class="custom-header-container" :class="{'title-left': titlePositionLeft}">
        <view class="custom-header" :class="{animation}" :style="{
				background: scrollBg
			}">

            <view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
            <view class="navbar" :style="{height: navHeight + 'px'}">
				<slot name="left">
					<view class="left" v-if="!titlePositionLeft">
						<view v-if="showBackBtn || !hasLeftContentWithScroll" class="back_icon" :style="{
							  backgroundImage: `url(${showHomeBtn ? homeIcon : IMG_URL + backIcon})`
						  }" :class="[showBackBtn ? '' : 'not-show', showHomeBtn ? 'show-home-btn' : '']" @click='onLeftClick'></view>
						<view v-if="hasLeftContentWithScroll" :style="{opacity:scrollOpacity}">
						    <slot name="leftContentWithScroll"></slot>
						</view>
					</view>
				</slot>
                
                <view class="title">
                    <slot name="title">{{ title }}</slot>
                </view>
                <view class="right"></view>
            </view>
            <view class="bottom-slot">
                <slot name='bottom'></slot>
            </view>
        </view>
        <view class="mask" :style="{
			height: totalHeight,
			background: bgComputed,
			backgroundSize: `100% ${bgForNav ? navHeight + 'px' : bgHeight}`,
			opacity: 1 - scrollOpacity
		}"></view>
        <view v-if="placeholder" class="custom-header_placeholder" :style="{height: totalHeight}">
            <view v-if="showLoading" class="refresh-icon">
                <text></text>
                <text></text>
                <text></text>
            </view>
        </view>
    </view>
</template>

<script>
import { IMG_URL, MAIN_COLOR } from '@/utils/config';
import homeIcon from "@/static/images/tabbar/home_on.png"
export default {
    name: "customHeader",
    props: {
        title: {
            type: String,
            default: ''
        },
        navheight: Number,
        placeholder: {
            type: Boolean,
            default: true
        },
        bg: String,
        autoBack: {
            type: Boolean,
            default: true
        },
        bgHeight: {
            type: String,
            default: 'auto'
        },
        bgColor: {
            type: String,
            default: MAIN_COLOR
        },
        showLoading: Boolean,
        isSharePage: Boolean,
        showBackBtn: {
            type: Boolean,
            default: true
        },
        scrollTop: {
            type: [Number, null],
			default: null
        },
        scrollRatio: {
            type: Number,
            default: 1
        },
        backIcon: {
            type: String,
            default: 'question-back.png'
        },
        bgForNav: Boolean,
		animation: Boolean,
		titlePositionLeft: Boolean
    },
    data () {
        return {
            statusBarHeight: 0,
            // #ifdef MP-WEIXIN
            navHeight: 0,
            // #endif
            // #ifdef APP || H5
            navHeight: 44,
            // #endif
            bottomSlotHeight: 0,
            IMG_URL,
			showHomeBtn: false,
			homeIcon
        };
    },
    computed: {
        totalHeight () {
            return this.statusBarHeight + this.navHeight + this.bottomSlotHeight + 'px';
        },
        scrollBg () {
            // return this.scrollTop && this.scrollTop > 0 ? `rgba(${this.parseColor(this.scrollBgColor)}, ${this.scrollOpacity})` : this.bg ? 'transparent' : this.bgColor;
			return (this.scrollTop && this.scrollTop > 0) 
			    ? `rgba(${this.parseColor(this.bgColor)}, ${this.scrollOpacity})` 
			    : ((this.bg || this.scrollTop !== null) 
			        ? 'transparent' 
			        : this.bgColor);
        },
        bgComputed () {
			// TIPS：☆☆☆☆☆ scrollTop的优先级最高，bg次之，bgColor最小。
            // return this.bg ? `#fff url(${this.imgUrl + this.bg}) no-repeat center top` : this.bgColor
            return this.scrollTop !== null ? this.scrollBg : this.bg ? `${this.bgColor} url(${this.IMG_URL + this.bg}) no-repeat center ${this.bgForNav ? this.statusBarHeight + 'px' : 'top'}` : this.scrollBg;
        },
        hasLeftContentWithScroll () {
            return this.$scopedSlots.leftContentWithScroll;
        },
        scrollOpacity () {
            return this.scrollTop > 100 * this.scrollRatio ? 1 : this.scrollTop / 100 / this.scrollRatio;
        }
    },
    mounted () {
        this.initPagesStack();
        // #ifdef MP-WEIXIN
        const menuButtonObject = uni.getMenuButtonBoundingClientRect();
        // #endif
        uni.getSystemInfo({
            success: res => {
                this.statusBarHeight = res.statusBarHeight;
                // #ifdef MP-WEIXIN
                this.navHeight = menuButtonObject.height + (menuButtonObject.top - res.statusBarHeight) * 2;
                // #endif
            }
        });
        // 获取
        // #ifdef MP-WEIXIN
        this.$nextTick(() => {
			setTimeout(() => {
				const query = uni.createSelectorQuery().in(this);
				query.select(".bottom-slot")
				    .boundingClientRect(rect => {
				        this.bottomSlotHeight = rect.height;
				        this.$emit('update:navheight', this.statusBarHeight + this.navHeight + rect.height);
				    })
				    .exec();
			}, 100)
		})
        // #endif
    },
    methods: {
        initPagesStack () {
            const pages = getCurrentPages();
			this.showHomeBtn = pages.length === 1
        },
        onLeftClick () {
            if (!this.showBackBtn) return;
            if (this.autoBack && !this.isSharePage && !this.showHomeBtn) {
                uni.navigateBack();
            } else {
                uni.switchTab({
                    url: '/pages/pages-tabs/home/<USER>'
                });
            }
            this.$emit('leftClick');
        },
        parseColor (color) {
            // 处理RGB格式
            if (color.startsWith('rgb(')) {
                return color.match(/\d+/g).join(',');
            }
            // 处理RGBA格式，忽略透明度
            else if (color.startsWith('rgba(')) {
                return color.match(/\d+/g).slice(0, 3).join(',');
            }
            // 处理十六进制格式
            else if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(color)) {
                let hex = color.substring(1); // 去掉井号
                if (hex.length === 3) { // 如果是简写形式，如"#fff"，则需要转换为6位
                    hex = hex.split('').map(c => c + c).join('');
                }
                return [
                    parseInt(hex.substring(0, 2), 16),
                    parseInt(hex.substring(2, 4), 16),
                    parseInt(hex.substring(4, 6), 16)
                ].join(',');
            } else {
                throw new Error('customHeader 不支持的颜色格式');
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.custom-header {
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: #fff;
    z-index: 9999;
	&.animation {
		animation-name: fadeIn;
		animation-duration: 1s;
		animation-fill-mode: forwards; /* 保持动画的最后一个状态 */
	}
}
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
.mask {
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 9998;
    box-sizing: border-box;
}
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    .left,
    .right,
    .left > view {
        width: 50rpx;
        height: 50rpx;
        &.not-show {
            opacity: 0;
        }
    }
    .back_icon {
        // @include backGroundUrl('jkbk_Previous_page.png');
        background-position: center;
        background-size: 50rpx 50rpx;
        background-repeat: no-repeat;
		&.show-home-btn {
			margin-top: 0;
			background-color: rgba(255, 255, 255, .5);
			border-radius: 50%;
			padding: 5rpx;
			transform: translate(-5rpx, -5rpx);
			background-size: 40rpx 40rpx;
		}
    }
    .title {
        font-weight: bold;
        font-size: 36rpx;
        color: $main-font-color;
        line-height: 1;
		// border: 1rpx solid red;
        text-align: center;
        font-style: normal;
        text-transform: none;
		transform: translateY(-2rpx);
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
    }
}
.custom-header_placeholder {
    display: flex;
    align-items: flex-end;
    padding-bottom: 30rpx;
    box-sizing: border-box;
}
.refresh-icon {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
    text {
        display: block;
        width: 15rpx;
        height: 15rpx;
        background: #999999;
        border-radius: 50%;
        animation: refreshAni 1s infinite 0.3s;
        &:nth-child(2) {
            animation-delay: 0.5s;
        }
        &:nth-child(3) {
            animation-delay: 0.7s;
        }
    }
}
@keyframes refreshAni {
    0% {
        transform: scale(0.5);
        opacity: 0.5;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.5);
        opacity: 0.5;
    }
}
</style>