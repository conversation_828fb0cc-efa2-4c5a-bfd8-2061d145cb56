
import { request } from '@/utils/request.js';

export const indexNotice = (data) => {
    return request({
        url: 'museum-app/app/noticeManage/indexDetail',
        method: 'get',
        data
    });
};
export const indexList = (data) => {
    return request({
        url: 'museum-app/app/culturalExplorationArticle/indexList',
        method: 'get',
        data
    });
};
//文化探索列表
export const culturList = (data) => {
    return request({
        url: 'museum-app/app/culturalExplorationArticle/list',
        method: 'get',
        data: data
    });
};
//文化探索详情
export const culturDetails = (data) => {
    return request({
        url: 'museum-app/app/culturalExplorationArticle/detail',
        method: 'get',
        data: data
    });
};
//文化探索轮播
export const culturSwiper = (data) => {
    return request({
        url: 'museum-app/app/culturalExplorationArticle/carousel',
        method: 'get',
        data
    });
};
//展厅公告列表
export const noticeList = (data) => {
    return request({
        url: 'museum-app/app/noticeManage/list',
        method: 'get',
        data: data
    });
};
//游玩须知
export const travelNotice = () => {
    return request({
        url: 'museum-app/app/playInstructions/detail',
        method: 'get',
    });
};

// 地图导览 museum-app/app/culturalExplorationArticle/hallMap
export const getMapGuide = () => {
    return request({
        url: 'museum-app/app/culturalExplorationArticle/hallMap',
        method: 'get',
    });
};
//扫码登录-app小程序调用 
export const scanCode = (data) => {
    return request({
        url: 'museum-activity/work-login-user/save-login',
        method: 'get',
        data
    });
};
//获取首页视频背景
export const indexVideo = (data) => {
    return request({
        url: 'museum-app/app/noticeManage/video',
        method: 'get',
    });
};
