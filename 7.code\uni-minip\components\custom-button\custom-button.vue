<template>
	<button
	 class="u-reset-button custom-button tap-style"
	  :class="[size, type, type ? 'has-type' : '', block ? 'block' : '']"
	  :open-type="openType"
	  :disabled="disabled"
	  @getphonenumber="$emit('getphonenumber', $event)"
	  @getuserinfo="$emit('getphonenumber', $event)"
	  @click="$emit('click')"
	  >
		<image v-if="icon" :src="icon" class="button-icon" mode="aspectFit"></image>
		<slot>按钮</slot>
	</button>
</template>

<script>
	export default {
		props: {
			size: String,
			disabled: Boolean,
			openType: String,
			type: String,
			block: Boolean,
			icon: String
		},
		options: {
			styleIsolation: "shared"
		}
	}
</script>

<style lang="scss" scoped>
	.custom-button {
		@include bgUrl('feedback-menu.png');
		width: 220rpx;
		height: 130rpx;
		line-height: 90rpx;
		font-weight: bold;
		font-size: 34rpx;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		color: $main-font-color;
		vertical-align: top;
		
		.button-icon {
			width: 30rpx;
			height: 30rpx;
			margin-right: 4rpx;
		}
		
		&.large {
			@include bgUrl('bg_btn_big.png');
			width: 620rpx;
			height: 90rpx;
			letter-spacing: 5rpx;
		}
		&.has-type {
			width: auto;
			height: auto;
			min-width: 150rpx;
			padding: 0 24rpx;
			height: 56rpx;
			box-sizing: border-box;
			line-height: 52rpx;
			background: none;
			font-weight: bold;
			font-size: 26rpx;
			border: 1rpx solid #EED3B1;
			background: linear-gradient(0deg, #DABA8B 0%, #F7DFC1 100%);
			border-radius: 8rpx;
			margin: 1rpx 0;
			&.info {
				background: #FAF9F4;
				color: #4D3B2E;
				border-color: #D1C9BA;
			}
			&.block {
				width: 100%;
				height: 90rpx;
				line-height: 80rpx;
				font-size: 34rpx;
			}
			&[disabled] {
				opacity: .9;
			}
		}
		&[disabled] {
			color: rgba(0, 0, 0, 0.1)!important;
		}
	}
</style>