{
    "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/pages-tabs/home/<USER>"
        },
        {
            "path": "pages/pages-tabs/mine/mine"
        },
        {
            "path": "pages/login/login"
        }
    ],
    "subPackages": [
        // 主页分包
        {
            "root": "pages/pages-index",
            "pages": [
                {
                    "path": "ticketBooking/ticketBooking"
                },
                {
                    "path": "ticketBooking/showNotice"
                },
                {
                    "path": "ticketBooking/submitOrder"
                },
                {
                    "path": "culturalExploration/index"
                },
                {
                    "path": "culturalExploration/details"
                },
                {
                    "path": "notice/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "notice/details"
                },
                {
                    "path": "ticketBooking/confirmOrder"
                },
				{
					 "path": "travelNotice/index"
				},
				{
					 "path": "mapGuide/mapGuide"
				},
				{
				    "path": "party/index"
				},
				{
				    "path": "party/getCode"
				},
				{
				    "path": "party/callNumber",
					"style": {
					    "enablePullDownRefresh": true
					}
				}
            ]
        },
        // 我的分包
        {
            "root": "pages/pages-my",
            "pages": [
                {
                    "path": "order/order",
					"style": {
					    "enablePullDownRefresh": true,
						"backgroundTextStyle": "light"
					}
                },
                {
                    "path": "order/details"
                },
                {
                    "path": "order/requestRefund"
                },
                {
                    "path": "question/index"
                },
                {
                    "path": "feedback/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "feedback/details"
                },
                {
                    "path": "feedback/feedback"
                },
                {
                    "path": "contactManage/addIndex"
                },
                {
                    "path": "contactManage/manageIndex"
                },
                {
                    "path": "changeSelfInfor/changeIndex"
                },
				{
				    "path": "myWork/index"
				},
                {
                    "path": "talkOnTheWall/index"
                },
				{
				    "path": "tangsongDynastyPaintings/index"
				},
				{
				    "path": "tangsongDynastyPaintings/detail"
				},
				{
				    "path": "tangsongDynastyPaintings/share"
				},
				{
				    "path": "writeOff/index"
				},
				{
				    "path": "writeOff/details"
				},
				{
				    "path": "writeOff/result"
				},
				{
				    "path": "writeOff/record",
					"style": {
					    "enablePullDownRefresh": true
					}
				},
				{
				    "path": "writeOff/hexiaoInfo"
				},
                {
                    "path": "protocol/index"
                }
            ]
        },
        {
            "root": "pages/pages-gbln",
            "pages": [
                {
                    "path": "ticketBooking/ticketBooking"
                },
                {
                    "path": "ticketBooking/showNotice"
                },
                {
                    "path": "ticketBooking/submitOrder"
                },
                {
                    "path": "ticketBooking/confirmOrder"
                },
                {
                    "path": "contactManage/addIndex"
                },
                {
                    "path": "contactManage/manageIndex"
                },
                {
                    "path": "order/order"
                },
                {
                    "path": "order/details"
                }
            ]
        }
    ],
    "easycom": {
        "autoscan": true,
        "custom": {
            "^uni-(.*)": "@/components/uni-$1.vue", // 匹配components目录内的vue文件
            "^vue-file-(.*)": "packageName/path/to/vue-file-$1.vue" // 匹配node_modules内的vue文件
        }
    },
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "uni-app",
        "navigationBarBackgroundColor": "#f7f7f2",
        "backgroundColor": "#f7f7f2",
        "navigationStyle": "custom",
        "fontFamily": "songti"
    },
    "uniIdRouter": {},
    //底部导航栏
    "tabBar": {
        "color": "#666363", //当前字体颜色
        "selectedColor": "#1A1818", //点击激活的字体颜色
        "borderStyle": "white",
        "backgroundColor": "#f7f7f2",
        "iconWidth": "20",
        "custom": true,
        "list": [
            {
                "text": "首页",
                "iconPath": "static/images/tabbar/home_off.png",
                "selectedIconPath": "static/images/tabbar/home_on.png",
                "pagePath": "pages/pages-tabs/home/<USER>"
            },
            {
                "text": "我的",
                "iconPath": "static/images/tabbar/my_off.png",
                "selectedIconPath": "static/images/tabbar/my_on.png",
                "pagePath": "pages/pages-tabs/mine/mine"
            }
        ]
    }
}