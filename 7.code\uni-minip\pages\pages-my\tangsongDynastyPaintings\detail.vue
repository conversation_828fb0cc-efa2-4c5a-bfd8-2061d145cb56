<template>
	<custom-page bg="bg_head_pro.png">
		<custom-header title="唐宋画境详情" :scroll-top="scrollTop" :scroll-ratio="2"></custom-header>
		<view class="tshj_detail">
			<!-- 详情页面内容为空 -->
			<view class="main-bg">
				<view class="image-box">
					<image class="person-image" :src="imageUrl" mode="widthFix" />
					<image :src="IMG_URL + 'tshj_details_border.png'" mode="widthFix" class="border-img" />
				</view>
				<view class="image-operations">
					<custom-button type="info" :icon="IMG_URL + 'tshj_icon_save.png'"
						@click="showSaveConfirm">保存本地</custom-button>
					<custom-button type="info" :icon="IMG_URL + 'tshj_icon_share.png'"
						@click="showShareConfirm">分享</custom-button>
					<custom-button type="primary" :icon="IMG_URL + 'tshj_icon_watermark.png'"
					:disabled="!notPaied"
						@click="showPaymentDialog">去水印</custom-button>
				</view>
			</view>
		</view>

		<!-- 保存确认弹窗 -->
		<custom-modal :visible.sync="showSaveDialog" title="是否将作品保存到本地相册？" :show-cancel="false">
			<template #bottomBar>
				<view class="dialog-btns">
					<custom-button type="info" @click="cancelSave">取消</custom-button>
					<custom-button type="info" @click="saveWithWatermark">带水印保存</custom-button>
					<custom-button type="primary" @click="showPaymentDialog">无水印保存</custom-button>
				</view>
			</template>
		</custom-modal>

		<!-- 支付弹窗 -->
		<custom-modal :visible.sync="showPayDialog" :show-cancel="false" confirm-text="去支付" @confirm="goToPay">
			<view class="payment-content" slot="title">
				<view class="label">去水印</view>
				<view class="payment-price">￥{{ payAmount }}</view>
			</view>
		</custom-modal>

		<!-- 分享确认弹窗 -->
		<custom-modal :visible.sync="showShareDialog" title="是否将作品分享？" :show-cancel="false">
			<template #bottomBar>
				<view class="dialog-btns">
					<custom-button type="info" @click="cancelShare">取消</custom-button>
					<custom-button type="info" @click="shareWithWatermark">带水印分享</custom-button>
					<custom-button type="primary" @click="showPaymentDialogForShare">无水印分享</custom-button>
				</view>
			</template>
		</custom-modal>

		<painter v-if="showPainter" class="painter-instance" :palette="palette" :scale-ratio="scaleRatio"
			@imgOK='onImgOk' @imgErr="onImgErr">
		</painter>

	</custom-page>
</template>

<script>
import { IMG_URL } from '@/utils/config'
import Painter from "uniapp-painter";
import poster from "./poster.js"
import { getAiFaceInfo, prepayByBestPayFace } from '@/api/aiFace';
import {
	payment
} from '@/utils';

export default {
	components: {
		Painter
	},
	data() {
		return {
			IMG_URL,
			scrollTop: 0,
			showSaveDialog: false,
			showPayDialog: false, 
			showShareDialog: false, // 添加分享弹窗控制字段
			palette: {},
			showPainter: false,
			scaleRatio: 1,
			// 添加一个标志位，用于区分是保存还是分享操作
			isShareOperation: false,
			initOperation: false,
			faceId: null,
			status: 0, // 未支付
			lockCostumeUrl: '',
			unlockCostumeUrl: '',
			orderSn: '',
			payAmount: 0
		};
	},
	computed: {
		notPaied() {
			return this.status == 0
		},
		imageUrl() {
			return this.notPaied ? this.lockCostumeUrl : this.unlockCostumeUrl
		}
	},
	onLoad({ faceId }) {
		this.faceId = faceId
		// this.palette = poster.palette;
		this.scaleRatio = uni.getSystemInfoSync().pixelRatio;
	},
	onShow() {
		console.log('on Show')
		this.init()
	},
	methods: {
		async init() {
			const data = await getAiFaceInfo({ faceId: this.faceId })
			console.log(data)
			this.status = data.status
			this.lockCostumeUrl = data.lockCostumeUrl
			this.unlockCostumeUrl = data.unlockCostumeUrl
			this.orderSn = data.orderSn
			this.payAmount = data.payAmount
			setTimeout(() => {
				this.palette = poster.setValues({
					mainImage: this.imageUrl
				})
			}, 100)

			// 查询status是否等于1，如果等于1，则执行分享操作 或者 保存到相册操作
			if (this.status == 1 && this.initOperation) {
				if (this.isShareOperation) {
					this.goToShare()
				} else {
					this.saveImageToAlbum(this.unlockCostumeUrl)
				}
			} else {
				if (this.initOperation) {
					uni.showToast({
						title: '未支付，操作失败',
						icon: 'none'
					})
				}
			}
		},
		showSaveConfirm() {
			if (!this.notPaied) {
				this.saveImageToAlbum(this.imageUrl);
				return
			}
			this.showSaveDialog = true;
			this.isShareOperation = false; // 标记为保存操作
		},
		cancelSave() {
			this.showSaveDialog = false;
		},
		saveWithWatermark() {
			// 带水印保存逻辑
			this.saveImageToAlbum(this.imageUrl);
		},
		saveImageToAlbum(url) {
			// 检查是否为网络图片
			if (url.startsWith('http')) {
				// 网络图片需要先下载到本地临时路径
				uni.showLoading({
					title: '保存中...'
				});
				
				uni.downloadFile({
					url: url,
					success: (res) => {
						if (res.statusCode === 200) {
							// 下载成功，保存到相册
							this.saveToAlbum(res.tempFilePath);
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '图片下载失败',
								icon: 'none'
							});
						}
					},
					fail: () => {
						uni.hideLoading();
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				});
			} else {
				// 本地图片直接保存
				this.saveToAlbum(url);
			}
		},
		// 权限校验函数，返回Promise
		checkPhotoAlbumPermission() {
			return new Promise((resolve, reject) => {
				uni.getSetting({
					success: (res) => {
						if (!res.authSetting['scope.writePhotosAlbum']) {
							// 没有权限，需要申请
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success: () => {
									console.log('授权成功');
									resolve();
								},
								fail: () => {
									// 申请权限失败，引导用户去设置
									uni.showModal({
										title: '提示',
										content: '需要相册权限，请去设置中开启',
										confirmText: '去设置',
										success: (modalRes) => {
											if (modalRes.confirm) {
												uni.openSetting({
													success: (settingRes) => {
														console.log(settingRes);
														// 用户从设置页面返回后，需要重新检查权限
														if (settingRes.authSetting['scope.writePhotosAlbum']) {
															resolve();
														} else {
															reject(new Error('用户拒绝相册权限'));
														}
													},
													fail: () => {
														reject(new Error('打开设置页面失败'));
													}
												});
											} else {
												reject(new Error('用户拒绝去设置开启权限'));
											}
										}
									});
								}
							});
						} else {
							// 已有权限
							resolve();
						}
					},
					fail: () => {
						reject(new Error('获取权限设置失败'));
					}
				});
			});
		},
		
		async saveToAlbum(filePath) {
			try {
				// 先检查权限
				await this.checkPhotoAlbumPermission();
				
				// 权限检查通过，保存图片到相册
				uni.saveImageToPhotosAlbum({
					filePath: filePath,
					success: () => {
						uni.hideLoading();
						uni.showToast({
							title: '已保存到相册',
							icon: 'success'
						});
						this.showSaveDialog = false;
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
						console.error('保存图片失败:', err);
					}
				});
			} catch (error) {
				uni.hideLoading();
				uni.showToast({
					title: error.message || '权限校验失败',
					icon: 'none'
				});
				console.error('权限校验失败:', error);
			}
		},
		showPaymentDialog() {
			this.initOperation = true
			this.showSaveDialog = false;
			this.showPayDialog = true;
			this.isShareOperation = false; // 标记为保存操作
		},
		// 添加用于分享的支付弹窗方法
		showPaymentDialogForShare() {
			this.initOperation = true
			this.showShareDialog = false;
			this.showPayDialog = true;
			this.isShareOperation = true; // 标记为分享操作
		},
		cancelPayment() {
			this.showPayDialog = false;
		},
		async goToPay() {
			// 支付逻辑
			uni.showToast({
				title: '正在前往支付',
				icon: 'loading'
			});
			await payment(this.orderSn, prepayByBestPayFace)
			this.showPayDialog = false;
			// await this.init()
		},
		// 添加分享确认相关方法
		showShareConfirm() {
			if (!this.notPaied) {
				this.goToShare();
				return
			}
			this.showShareDialog = true;
			this.isShareOperation = true; // 标记为分享操作
		},
		cancelShare() {
			this.showShareDialog = false;
		},
		shareWithWatermark() {
			// 带水印分享逻辑
			this.goToShare();
		},
		goToShare() {
			uni.showLoading({
				title: '正在生成海报'
			})
			this.showPainter = true
			this.showShareDialog = false; // 关闭分享弹窗
		},
		// 支付后执行分享操作
		goToShareAfterPay() {
			uni.showLoading({
				title: '正在生成海报'
			})
			this.showPainter = true
		},
		onImgOk(e) {
			console.log('onImgOk',e);
			uni.showShareImageMenu({
				path: e.detail.path,
				success: res => {
					console.log(res)
				},
				fail: err => {
					console.log(err)
				},
				complete: () => {
					this.showPainter = false
					uni.hideLoading(); 
				}
			})
		},
		onImgErr(err) {
			console.log('onImgErr',err);
		}
	},
	onPageScroll({ scrollTop }) {
		this.scrollTop = scrollTop;
	},
}
</script>

<style lang="scss" scoped>
.tshj_detail {
	padding: 30rpx;
	.main-bg {
		height: 1188rpx;
		@include bgUrl("tshj_details_bg.png");
		background-size: 100% 100%;
		display: flex;
		flex-direction: column;
		// justify-content: center;
		align-items: center;
		padding-top: 133rpx;
		box-sizing: border-box;
		.image-box {
			width: 608rpx;
			height: 904rpx;
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			image {
				display: block;
				width: 590rpx;
				height: 886rpx;
			}
			.border-img {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
		.image-operations {
			padding-top: 40rpx;
			display: flex;
			justify-content: center;
			gap: 20rpx;
		}
	}
}

.payment-content {
	padding: 20rpx;
}

.dialog-btns {
	display: flex;
	justify-content: center;
	gap: 30rpx;
}
.payment-content {
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 1;
	gap: 44rpx;
	.label {
		font-weight: bold;
		font-size: 28rpx;
		color: #1A1818;
	}
	.payment-price {
		font-weight: bold;
		font-size: 32rpx;
		color: #B36859;
	}
}
</style>