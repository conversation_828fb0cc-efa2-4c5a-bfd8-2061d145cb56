!<script>
	import { getProtocol } from '@/api/login'
    export default {
		data() {
			return {
				content: '',
				type: 1
			}
		},
		onLoad({ type }) {
			this.init(type)
		},
		methods: {
			async init(type) {
				const data = await getProtocol({ type })
				this.content = data
			}
		}
    }
</script>

<template>
    <custom-page>
        <custom-header title="协议详情"></custom-header>
        <view class="protocol-content">
            <u-parse :content="content"></u-parse>
        </view>
    </custom-page>
</template>

<style scoped lang="scss">
    .protocol-content {
        padding: 20rpx;
    }
</style>