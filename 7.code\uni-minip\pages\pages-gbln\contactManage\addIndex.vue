<template>
    <custom-page :bg-top="navHeight">
        <custom-header :title="`${isEdit ? '编辑' : '新增'}联系人`" :navheight.sync="navHeight"></custom-header>
        <view class="topLine"></view>
        <view class="box">
            <view class="content">
                <view class="box-num">
                    <view class="num">
                        <text class="boxType-text">*</text>
                        <view class="num-r">中文姓名</view>
                    </view>
                    <input class="input" v-model="info.name" placeholder="请填写" />
                </view>
                <view class="box-num">
                    <view class="num">
                        <text class="boxType-text">*</text>
                        <view class="num-r">联系方式</view>
                    </view>
                    <input class="input" v-model="info.phone" maxlength="11" type="number" placeholder="请填写" />
                </view>

                <view class="box-type">
                    <view class="boxType-l">
                        <text class="boxType-text">*</text>
                        <text class="boxType-texts">证件类型</text>
                    </view>

                    <view class="boxType-r" @click="select">
                        <view class="boxTypeR-text">{{info.label}}</view>
                        <view class="boxTypeR-img"></view>
                    </view>
                </view>
                <view class="box-num">
                    <view class="num">
                        <text class="boxType-text">*</text>
                        <view class="num-r">证件号</view>
                    </view>
                    <input class="input" v-model="info.credentialNo" placeholder="请填写" />
                </view>
            </view>
        </view>
        <view class="bottomLine"></view>
        <view class="box-bottom">
            <checkbox-group @change="onCheckboxChange">
                <label>
                    <checkbox :value='1' :checked="agreeStatus"></checkbox>
                    <view class="custom-check"></view>
                    <view class="protocal-text">阅读并同意以下内容:</view>
                </label>
            </checkbox-group>
        </view>
        <view class="box-bottom-text">
            <text class="argee-text">
                1、您已知晓您在辽宁省博物馆数字展厅小程序录入的身份证件信息，将用于您预订门票等所有需要实名制的旅游产品及数字展厅内游戏中，并在使用时进行验证，请确保此信息的真实有效。辽宁省博物馆数字展厅小程序将通过加密等方式保护此信息。
                2、如果使用身份证以外证件进行购票，不支持刷身份证进入数字展厅。
            </text>
        </view>
        <custom-bottombar>
            <view class="flex_jus_sb">
                <custom-button v-if="isEdit" type="info" class="mr30" @click="deleteUser">删除</custom-button>
                <custom-button type="primary" :disabled="saveLoading || !disabledBtn" @click="saveUser">保存</custom-button>
            </view>
        </custom-bottombar>
        <picker ref="pic" :picklist="picklist" @echo="echo"></picker>
        <custom-modal :visible.sync="showVisible" confirmText="删除" :title="modalTtile" @cancel="cancel"
            @confirm="confirm"></custom-modal>
    </custom-page>
</template>

<script>
import { saveGblnUser as saveUser, updateGblnUser as updateUser } from "@/api/guobaoln";
import picker from '@/components/custom-picker/picker.vue';
export default {
    components: {
        picker
    },
    data () {
        return {
            info: {
                name: "", //姓名
                phone: "", //手机号
                documentType: "", //证件类型
                credentialNo: "", //证件编码
                label: '请选择',

            },
            navHeight: 0,
            redisable: false,
            files: [],
            value: '',
            picklist: [
                {
                    label: '身份证',
                    value: '1'
                },
                // {
                //     label: '投诉',
                //     value: '2'
                // }
            ],
            disable: false, //防止重复点击
            agreeStatus: false,
            isUpdate: false,//是否编辑
            showVisible: false,//展示删除弹窗
            params: {},//删除联系人参数
            modalTtile: '',//删除弹窗标题
            saveLoading: false,
            isEdit: false
        };
    },
	computed: {
		disabledBtn() {
			const { name, phone, documentType, credentialNo } = this.info
			return name && phone && documentType && credentialNo && this.agreeStatus
		}
	},
    methods: {
        onCheckboxChange (e) {
            this.agreeStatus = e.detail.value.length > 0;
        },
        // 手机号校验
        checkPhone (phone) {
            if (!(/^1[3456789]\d{9}$/.test(phone))) {
                return false;
            }
            return true;
        },
        select () {
            this.$refs.pic.shows();
        },
        echo (v) {
            this.info.label = v.target.__args__[0].label || '身份证';
            this.info.documentType = v.target.__args__[0].value || 1;
        },
        cancel () {
            console.log('关闭弹窗');
        },
        // 确认删除
        confirm () {
            deleteItem(this.params).then((res) => {
                if (res) {
                    uni.showToast({
                        title: '删除成功',
                        icon: 'none'
                    });
                    setTimeout(() => {
						uni.navigateBack()
					}, 1000)
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
        // 删除联系人
        deleteUser () {
            this.showVisible = true;
            this.modalTtile = '确定删除联系人' + this.info.name + '?';
            let params = {
                id: this.info.id
            };
            this.params = params;
        },
        // 保存新增联系人
        saveUser () {
            if (!this.agreeStatus) {
                uni.showToast({
                    title: '请同意须知',
                    icon: 'none'
                });
            } else {
                this.saveLoading = true;
                uni.showLoading({
                    title: '请稍后...'
                });
                if (!this.checkPhone(this.info.phone)) {
                    uni.showToast({
                        title: '请输入正确手机号',
                        icon: 'none'
                    });
                    this.saveLoading = false;
                } else {
                    if (this.isUpdate) {
                        let params = {
                            id: this.info.id,
                            name: this.info.name,
                            phone: this.info.phone,
                            documentType: this.info.documentType,
                            credentialNo: this.info.credentialNo,
                        };
                        updateUser(params).then((res) => {
                            if (res) {
                                uni.showToast({
                                    title: '保存成功',
                                    icon: 'none'
                                });
                                setTimeout(() => {
                                    uni.navigateBack();
                                }, 600);
                            } else {
                                uni.$u.toast(res.msg);
                            }
                        }).finally(() => {
                            this.saveLoading = false;
                        });
                    } else {
                        saveUser(this.info).then((res) => {
                            if (res) {
                                uni.showToast({
                                    title: '保存成功',
                                    icon: 'none'
                                });
                                setTimeout(() => {
                                    uni.navigateBack();
                                }, 600);
                            } else {
                                uni.$u.toast(res.msg);
                            }
                        }).finally(() => {
                            this.saveLoading = false;
                        });
                    }
                }


            }
        },
    },
    onLoad (OPTION) {
        this.isEdit = OPTION.type == '2'
        this.info.documentType = 1;
        if (OPTION.newItem) {
            this.info = JSON.parse(OPTION.newItem);
        }
        if (this.info.documentType == 1) {
            this.info.label = '身份证';
        }
        if (OPTION.newItem) {
            this.isUpdate = true;
            this.agreeStatus = true;
        } else {
            this.isUpdate = false;
            this.agreeStatus = false;
        }
    },
    onShow () {

    }
}
</script>

<style lang="scss">
.topLine {
    width: calc(100% - 20rpx);
    height: 26rpx;
    margin-left: 10rpx;
    @include bgUrl('feedback-top.png');
    background-size: 100% 100% !important;
}
.bottomLine {
    width: calc(100% - 20rpx);
    height: 26rpx;
    margin-top: -4rpx;
    margin-left: 10rpx;
    @include bgUrl('feedback-bottom.png');
    background-size: 100% 100% !important;
}
//底部按钮
.menu {
    width: 100%;
    text-align: center;
    margin-top: 30rpx;
}
.boxType-text {
    width: 12rpx;
    height: 12rpx;
    line-height: 20rpx;
    margin-right: 10rpx;
    color: $sec-font-color;
    font-weight: 400;
}
.protocal-text {
    font-weight: 400;
    font-size: 24rpx;
    color: $tip-font-color;
}
.box-bottom-text {
    width: calc(100% - 60rpx);
    margin: 0 30rpx;
    .argee-text {
        font-weight: 400;
        font-size: 24rpx;
        color: $sec-font-color;
    }
}

.box-bottom {
    width: calc(100% - 60rpx);
    margin-left: 30rpx;
    margin-top: 37rpx;
    .custom-check {
        width: 28rpx;
        height: 28rpx;
        @include bgUrl('icon_checkbox_inchecked.png');
        margin-right: 10rpx;
        margin-top: 6rpx;
    }
    label {
        display: flex;
    }
    checkbox {
        display: none;
        &[checked] {
            & + .custom-check {
                @include bgUrl('icon_checkbox_checked.png');
                pointer-events: none;
            }
        }
    }
}

.box {
    width: calc(100% - 50rpx);
    margin-top: -4rpx;
    margin-left: 25rpx;
    background: #e8e2d4;
    overflow: hidden;
    .content {
        margin: 10rpx;
        background: #ffffff;
        overflow: hidden;
        .box-type {
            width: calc(100% - 40rpx);
            height: 100rpx;
            margin-left: 20rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1rpx solid #d0c9bc;
            box-sizing: border-box;
            .boxType-l {
                height: 26rpx;
                line-height: 26rpx;
                display: flex;
                align-items: center;

                .boxType-texts {
                    height: 26rpx;
                    line-height: 26rpx;
                    font-size: 28rpx;
                    font-weight: 400;
                }
            }
            .boxType-r {
                height: 50rpx;
                line-height: 50rpx;
                display: flex;
                align-items: center;
                .boxTypeR-text {
                    height: 30rpx;
                    line-height: 30rpx;
                    font-size: 28rpx;
                    color: $tip-font-color;
                    font-weight: 400;
                    margin-right: 10rpx;
                }
                .boxTypeR-img {
                    width: 50rpx;
                    height: 50rpx;
                    line-height: 50rpx;
                    @include bgUrl('feedback-return.png');
                    background-size: 100% 100% !important;
                }
            }
        }

        .box-name {
            width: calc(100% - 40rpx);
            height: 100rpx;
            margin-left: 20rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1rpx solid #d0c9bc;
            border-bottom: 1rpx solid #d0c9bc;
            box-sizing: border-box;
            .name {
                margin-left: 20rpx;
                font-size: 28rpx;
                font-weight: 400;
            }
            .input {
                text-align: right;
            }
        }
        .box-num {
            width: calc(100% - 40rpx);
            height: 100rpx;
            margin-left: 20rpx;
            border-bottom: 1rpx solid #d0c9bc;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;
            .num {
                height: 30rpx;
                line-height: 30rpx;

                display: flex;
                align-items: center;
                .num-l {
                    font-weight: bold;
                    font-size: 28rpx;
                    color: $sec-font-color;
                    margin-right: 12rpx;
                }
                .num-r {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #1a1818;
                }
            }
            .input {
                text-align: right;
                vertical-align: center;
                margin-right: 55rpx;
                font-size: 28rpx;
                width: 65%;
                height: 50rpx;
                font-size: 28rpx;
                color: $tip-font-color;
                line-height: 40rpx;
                font-family: 'songti' !important;
            }
        }
    }
}
.flex_jus_sb {
    justify-content: flex-end;
}
</style>
