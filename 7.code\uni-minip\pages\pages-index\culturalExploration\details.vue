<template>
	<custom-page>
	    <custom-header title="文化探索"></custom-header>
	    <view class="box">
	    	 <view class="title">{{info.articleTitle}}</view>
			 <view class="content">
				 	<u-parse :content="info.content" :tagStyle="style"></u-parse>
			 </view>
	    </view>
	</custom-page>
</template>

<script>
    import {culturDetails} from "@/api/home.js"
	export default {
		data() {
			return {
			   info:{},
			   style: {
			   	img: 'display:flex;margin:20rpx 0;'
			   }
			};
		},
		methods:{
		   getDetails(infoId){
			   let params = {
				   infoId:infoId
			   }
			   culturDetails(params).then((res)=>{
				   this.info = res
			   })
		   }
		},
		onLoad(option){
		   if(option&&option.infoId){
			   this.getDetails(option.infoId)
		   }
		}
	}
</script>

<style lang="scss">
.box{
	width: calc(100% - 40rpx);
	margin-left: 20rpx;
	overflow: hidden;
	.title{
		width: 100%;
		text-align: center;
		font-family: SourceHanSerifCN, SourceHanSerifCN;
		font-weight: bold;
		font-size: 30rpx;
		color: $main-font-color;
		margin: 40rpx 0;
	}
}
.content {
	word-break: break-all;
}
</style>
