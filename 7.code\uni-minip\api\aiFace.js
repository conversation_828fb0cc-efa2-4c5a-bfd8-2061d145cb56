import { request } from '@/utils/request.js';
// ai-face/getListByUserId
export const getAiFaceList = () => {
    return request({
        url: 'museum-app/ai-face/getListByUserId',
        method: 'get',
    });
};
// ai-face/delete
export const deleteAiFace = (data) => {
    return request({
        url: 'museum-app/ai-face/delete',
        method: 'get',
        data
    });
};
// /ai-face/getInfoById
export const getAiFaceInfo = (data) => {
    return request({
        url: 'museum-app/ai-face/getInfoById',
        method: 'get',
        data
    });
};

// prepayByBestPayFace
export const prepayByBestPayFace = (data) => {
    return request({
        url: 'museum-app/order/prepayByBestPayFace',
        method: 'post',
        data
    });
}
// /ai-face/save
export const saveAiFace = (data) => {
    return request({
        url: 'museum-app/ai-face/save',
        method: 'post',
        data
    });
}