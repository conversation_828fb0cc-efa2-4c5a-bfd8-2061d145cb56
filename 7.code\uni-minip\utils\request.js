
import store from "@/store/index.js";
import { BASE_URL } from "./config";

export const request = (options) => {
    return new Promise((resolve, reject) => {
        const header = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${store.state.token}`,
            ...options.header
        };

        uni.request({
            url: BASE_URL[process.env.NODE_ENV] + options.url,
            method: options.method || 'GET',
            data: options.data || {},
            header,
            success ({ data }) {
                const { msg, code } = data;

                if (code === 401) {
                    uni.showToast({
                        title: '登录失效，请登录',
                        icon: 'none'
                    });
                    store.dispatch('LOGOUT');
                    setTimeout(() => {
                        uni.redirectTo({
                            url: '/pages/login/login'
                        });
                    }, 1000);
                    return reject(msg || '登录失效，请登录');
                }

                if (code !== 200) {
                    uni.showToast({
                        title: msg || '服务器开小差了~',
                        icon: 'none'
                    });
					// throw new Error(msg || '接口错误')
                    return reject(msg || '服务器开小差了~');
                }
                resolve(data.data || data || {});
            },
            fail: (err) => {
                uni.showToast({
                    title: '网络错误',
                    icon: 'none'
                });
                reject(err);
            }
        });
    });
};