<template>
	<custom-page >
		<custom-header title="公告详情"></custom-header>
		<view class="box">
			<view class="title">{{info.noticeTitle}}</view>
			<view class="time">{{info.createTime}}</view>
			<view class="content">
				<u-parse :content="info.noticeContent" :tagStyle="style" class="parse-class"></u-parse>
			</view>
		</view>
	</custom-page>
</template>

<script>
	export default {
		data() {
			return {
				info:{},
				style: {
					img: 'display:flex;margin:20rpx 0;',
					p:'margin-bottom:10rpx'
				}
			};
		},
		methods:{
			
		},
		onLoad(option){
			if(option.item){ 
				this.info = JSON.parse(decodeURIComponent(option.item));
			}
		}
	}
</script>

<style lang="scss">
.box{
	width: calc(100% - 40rpx);
	margin-left: 20rpx;
	.title{
		width: 100%;
		text-align: center;
		margin-top: 54rpx;
		margin-bottom: 30rpx;
		font-weight: bold;
		font-size: 30rpx;
		color: $main-font-color;
	}
	.time{
		width: 100%;
		text-align: center;
		// margin-bottom: 28rpx;
		font-weight: 400;
		font-size: 22rpx;
		color: $tip-font-color;
	}
	.content{
		font-weight: 400;
		font-size: 26rpx;
		color:  $tip-font-color;
		overflow: hidden;
	}
}
</style>
