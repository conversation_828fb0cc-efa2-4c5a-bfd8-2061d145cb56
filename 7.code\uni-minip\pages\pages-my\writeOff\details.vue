<template>
	<custom-page bg="notice-bg.png">
	    <custom-header title="门票信息" bg-color="transparent"></custom-header>
		<view class="ticket" v-if="orderSource!='4'&&orderSource!='6'">
			<view class="ticket-title">
				<view class="tt-l"></view>
				<view class="tt-r">门票信息</view>
			</view>
			<view class="ticket-content">
				<view class="content" v-for="(item,index) in list">
					 <view class="c-l">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
				<view class="contents">
					<view class="cs-l">{{name}}</view>
					<view class="cs-r">{{value}}</view>
				</view>
			</view>
			<view class="ticket-end"></view>
		</view>
		<view class="info" v-if="orderSource!='4'&&orderSource!='6'">
			<view class="info-top">
				<view class="tt-l"></view>
				<view class="tt-r">游客信息</view>
			</view>
			<view class="info-mid">
				<view class="content" v-for="(item,index) in listT">
					 <view class="c-l" v-if="item.so==true"> <view class="circular"></view> <view class="text">{{item.name}}</view> </view>
					 <view class="c-l" v-if="item.so==false">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
			</view>
			<view class="info-bottom"></view>
		</view>
		<view class="tickets" v-if="orderSource=='4'||orderSource=='6'">
			<view class="ticket-title">
				<view class="tt-l"></view>
				<view class="tt-r">门票信息</view>
			</view>
			<view class="ticket-content">
				<view class="content" v-for="(item,index) in listThree">
					 <view class="c-l">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
			</view>
			<view class="ticket-end"></view>
		</view>
		
		
		<custom-button class="menu" @tap="really">确认核销</custom-button>
	</custom-page>
</template>
<script>
	import {writeOff} from "@/api/my.js"
	export default {
		data() {
			return {
				datas:null,
				  list:[
					  {
						  name:'使用日期',
						  value:''
					  },
					  {
						  name:'场次时间',
						  value:''
					  },
					  {
						  name:'券码状态',
						  value:''
					  },
					  {
						  name:'订单编码',
						  value:''
					  }
				  ],
				  listT:[
					  {
						  name:'游客姓名',
						  value:'迪丽扎哈',
						  so:false
					  },
					  {
					  	  name:'手机号',
					  	  value:'12312313123',
						  so:true
					  },
					  {
					  	  name:'身份证号',
					  	  value:'12312313123',
						  so:true
					  },
				  ],
				  listThree:[
					  {
						  name:'门票来源',
						  value:'工作票'
					  },
					  {
						  name:'券码状态',
						  value:''
					  }
				  ],
				 name:'订单状态', 
				 value:'',
				 id:'',
				 orderSource:''
			};
		},
		onLoad(opt){
			if(opt && opt.datas){
				this.datas = JSON.parse(decodeURIComponent(opt.datas))
				this.list[0].value = this.datas.suitDate
				this.list[1].value = this.datas.sessionDate
				this.list[2].value = this.datas.statusText
				this.list[3].value = this.datas.orderSn
				this.listT[0].value = this.datas.contactsName
				this.listT[1].value = this.datas.contactsPhone
				this.listT[2].value = this.datas.contactsCredentialNo
				this.value = this.datas.orderStatusText
				this.id = this.datas.id
				this.orderSource = this.datas.orderSource
				this.listThree[1].value = this.datas.statusText
			}
		},
		methods:{	
			really(){
				let params = {
					id:this.id
				}
				writeOff(params).then((res)=>{
					if(res.code==200){
						uni.redirectTo({
							url:'/pages/pages-my/writeOff/result?status=核销成功'
						});
					}else{
						uni.redirectTo({
							url:'/pages/pages-my/writeOff/result?status=核销失败'
						});
					}
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
.menu{
	width: 100%;
	display: flex;
	justify-content: center;
	position: fixed;
	bottom: 80rpx;
}
.info{
	width: calc(100% - 40rpx);
	height: 330rpx;	
	margin-top: 20rpx;
	margin-left: 20rpx;
	.info-top{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
	.info-mid{
		width: 100%;
		height:180rpx;
		background-color: #FFFFFF;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				height: 50rpx;
				line-height: 50rpx;
				display: flex;
				align-items: center;
				.circular{
					width: 6rpx;
					height: 6rpx;
					background: #D1C9BA;
					border-radius: 50%;
					margin-right: 14rpx;
				}
				.text{
					font-weight: 400;
					font-size: 26rpx;
					color: $tip-font-color;
				}
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
	}
	.info-bottom{
		width: 100%;
		height: 30rpx;
		background: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
	}
}
.ticket{
	width: calc(100% - 40rpx);
	height: 470rpx;
	margin-left: 20rpx;
	margin-top: 20rpx;
	display: flex;
	flex-direction: column;
	.ticket-content{
		width: 100%;
		height:320rpx;
		background-color: #FFFFFF;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-top: 10rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
		.contents{
			width: 100%;
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			overflow: hidden;
			.cs-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				margin-top: -10rpx;
			}
			.cs-r{
				width: 118rpx;
				height: 52rpx;
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $sec-font-color;
				display: flex;
				justify-content: center;
				align-items: center;
				@include bgUrl("order_status_bg.png");
				background-size: 100% 100%;
			}
		}
	}
	.ticket-title{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
    .ticket-end{
		width: 100%;
		height: 30rpx;
		background-color: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
	}
}
.tickets{
	width: calc(100% - 40rpx);
	height: 270rpx;
	margin-left: 20rpx;
	margin-top: 20rpx;
	display: flex;
	flex-direction: column;
	.ticket-content{
		width: 100%;
		height:150rpx;
		background-color: #FFFFFF;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-top: 10rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
		.contents{
			width: 100%;
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			overflow: hidden;
			.cs-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				margin-top: -10rpx;
			}
			.cs-r{
				width: 118rpx;
				height: 52rpx;
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $sec-font-color;
				display: flex;
				justify-content: center;
				align-items: center;
				@include bgUrl("order_status_bg.png");
				background-size: 100% 100%;
			}
		}
	}
	.ticket-title{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
    .ticket-end{
		width: 100%;
		height: 30rpx;
		background-color: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
	}
}
</style>