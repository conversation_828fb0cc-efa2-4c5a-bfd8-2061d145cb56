<template>
	<view class="custom-page" :class="{'safe-bottom': safeAreaInsetBottom}" :style="{background: bg ? 'transparent' : MAIN_COLOR}">
		<view v-if="bg" class="bg" :style="{
			backgroundImage: `url(${IMG_URL + bg})`,
			backgroundPosition: `center ${bgTop}px`
		}"></view>
		<slot></slot>
		
		<u-loading-page :loading="showLoading"></u-loading-page>
		<custom-empty v-show="empty" :text="emptyText"></custom-empty>
	</view>
</template>

<script>
	import { MAIN_COLOR, IMG_URL } from '@/utils/config';
	export default {
		name:"customPage",
		props: {
			bg: String,
			bgTop: {
				type: Number,
				default: 0
			},
			safeAreaInsetBottom: {
				type: Boolean,
				default: true
			},
			empty: Boolean,
			emptyText: String,
			showLoading: Boolean
		},
		data() {
			return {
				IMG_URL,
				MAIN_COLOR
			};
		}
	}
</script>

<style lang="scss" scoped>
.custom-page {
	box-sizing: border-box;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	&.safe-bottom {
		padding-bottom: calc(constant(safe-area-inset-bottom) + 30rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
	}
	.bg {
		width: 100%;
		height: 100%;
		position: fixed;
		background-repeat: no-repeat;
		background-size: 100% auto;
		left: 0;
		top: 0;
		z-index: -999;
	}
}
</style>