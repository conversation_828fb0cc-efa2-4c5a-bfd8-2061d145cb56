<template>
    <custom-page :bg-top="navHeight" :empty="listUser.length === 0">
        <custom-header title="联系人管理" :navheight.sync="navHeight">
			<view class="tooltip" slot="bottom">*最多添加20人，已添加{{listUser.length}}人。</view>
		</custom-header>
        
        <view class="content">
			<view class="recomm-scroll">
            <!-- <scroll-view class="recomm-scroll" :scroll-y="true" :scroll-top="scrollTop" @scrolltolower="scrollBottom"> -->
                <view class="list-item" v-for="(item,index) in listUser" :key="index">
                    <view class="title-bg"></view>
                    <view class="item-title">
                        <view class="item-title-left">{{item.name}}</view>
                        <view class="item-title-right" v-if="isAddUser">
                            <custom-checkbox :checked="item.agreeStatus" @change="onCheckboxChange($event,item)"></custom-checkbox>
                        </view>
                    </view>
                    <view class="item-body">
                        <view class="left-prop">
                            <text class="icon-point"></text>
                            <text class="prop-label">手机号</text>
                        </view>
                        <view class="right-ans">{{item.phone}}</view>
                    </view>
                    <view class="item-body">
                        <view class="left-prop">
                            <text class="icon-point"></text>
                            <text class="prop-label">身份证</text>
                        </view>
                        <view class="right-ans">{{encryptIdCard(item.credentialNo)}}</view>
                    </view>
                    <view class="item-bottom">
                        <image class="left-icon" :src="IMG_URL + 'icon_delete_user.png'" @click="deleteUser(item)">
                        </image>
                        <image class="btn-icon" :src="IMG_URL + 'icon_edit_user.png'" @click="addUser(2,item)"></image>
                    </view>
                </view>
            <!-- </scroll-view> -->
			</view>
        </view>
        <custom-bottombar>
            <view class="flex_jus_sb">
                <custom-button class="left" type="primary" @click="addUser(1)">新增联系人</custom-button>
                <custom-button type="primary" @click="saveUser" v-if="isAddUser">确认选择{{userLength}}人</custom-button>
            </view>
        </custom-bottombar>
        <custom-modal :visible.sync="showVisible" confirmText="删除" :title="modalTtile" @cancel="cancel"
            @confirm="confirm"></custom-modal>
    </custom-page>
</template>

<script>
import { saveUser, deleteItem, indexList } from "@/api/my.js";
import { IMG_URL } from '@/utils/config';
import{ mapState } from "vuex"

import picker from '@/components/custom-picker/picker.vue';
import config from '@/utils/config.js';
import CustomModal from '@/components/custom-modal/custom-modal.vue';
import CustomCheckbox from '@/components/custom-checkbox/custom-checkbox.vue';
export default {
    components: {
        picker,
        CustomModal,
        CustomCheckbox
    },
    data () {
        return {
            listUser: [],
            IMG_URL,
            navHeight: 0,
            redisable: false,
            files: [],
            label: '请选择',
            value: '',
            //滚动条配置项
            scrollTop: 0,
            old: {
                scrollTop: 0
            },
            picklist: [],
            disable: false, //防止重复点击
            agreeStatus: false,
            search: {
                pageNum: 1,
                pageSize: 25,
            },
            total: 0,
            selectArray: [],//已选择联系人
            userLength: 0, //已添加的联系人数量
            showVisible: false,//展示删除弹窗
            modalTtile: '',
            params: {},//删除联系人参数
            isAddUser: false,//是否添加联系人
            newUserArray: [],//接受参数
            newSelectArray: [],//已选联系人存值
        };
    },
    computed: {
        ...mapState(['userSelect'])
    },
    methods: {
        //新增联系人
        addUser (value, item) {
            let newItem = {};
            if (item) {
                newItem = JSON.stringify(item);
            } else {
                newItem = '';
            }
            uni.navigateTo({
                url: "/pages/pages-my/contactManage/addIndex?type=" + value + "&newItem=" + newItem
            });
        },
        encryptIdCard (value) {
            if (!value || value.length !== 18) return value;
            return value.substring(0, 3) + '******' + value.substring(14);
        },
        onCheckboxChange (e, item) {
            // this.agreeStatus = e.detail.value.length > 0;
            item.agreeStatus = !item.agreeStatus;
            if (item.agreeStatus) {
                if (this.selectArray.indexOf(item) == -1) {
                    this.selectArray.push(item);
                }
                this.userLength = this.selectArray.length;
            } else {
                this.selectArray.filter((element, index) => {
                    if (element.id == item.id) {
                        this.selectArray.splice(index, 1);
                    }
                });
                this.userLength = this.selectArray.length;
            }
            if (this.selectArray.length > 20) {
                uni.showToast({
                    title: '最多添加20人',
                    icon: 'none'
                });
            }
        },
        scrollBottom () {
            let that = this;
            if (this.search.pageNum * this.search.pageSize >= this.total) {
                return false;
            } else {
                this.search.pageNum++;
            }
            this.init();
        },
        scrollTopUp (e) {
            console.log('滚动到顶部', e);
        },
        cancel () {
            console.log('关闭弹窗');
        },
        async init (value) {
            let data1 = {
                pageNum: this.search.pageNum,
                pageSize: this.search.pageSize
            };
            const data = await indexList(data1);
            this.listUser = data.map(item => {
                item.agreeStatus = this.userSelect.some(s => s.id === item.id)
                return item
            });
        
            if (this.isAddUser) {
                this.selectArray = this.listUser.filter(item => item.agreeStatus);
                this.userLength = this.selectArray.length;
                this.$store.commit('USER_ADD', this.selectArray);
            } else {
                this.userLength = this.listUser.length;
            }
            // 是否为删除
            if (value) {
                let deleteArray = [];
                for (let item = 0; item < this.selectArray.length; item++) {
                    const element = this.selectArray[item];
                    for (let item2 = 0; item2 < this.listUser.length; item2++) {
                        const element2 = this.listUser[item2];
                        if (element.id == element2.id) {
                            deleteArray.push(element);
                            element2.agreeStatus = true;
                        }
                    }
                }
                this.userLength = deleteArray.length;
                this.selectArray = deleteArray;
            }
        },
        // 确认删除
        confirm () {
            deleteItem(this.params).then((res) => {
                if (res) {
                    uni.showToast({
                        title: '删除成功',
                        icon: 'none'
                    });
                    // 是否为删除
                    this.init(1);
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
        // 删除联系人
        deleteUser (item) {
            this.showVisible = true;
            this.modalTtile = '确定删除联系人' + item.name + '?';
            let params = {
                id: item.id
            };
            this.params = params;
        },
        // 保存新增联系人,传给订单
        saveUser () {
            console.log(this.selectArray);
            this.$store.commit('USER_ADD', this.selectArray);
            setTimeout(() => {
                uni.navigateBack();
            }, 600);
        },
    },
    onLoad (option) {
        this.isAddUser = option.isAddUser == 'true';
    },
    /* onUnload () {
        this.$store.commit('USER_ADD', []);
    }, */
    onShow () {
        this.init();

    }
}
</script>

<style lang="scss">
.tooltip {
    font-weight: bold;
    font-size: 26rpx;
    color: #b36859;
    // margin-left: 30rpx;
	padding: 10rpx 30rpx 20rpx;
}
.content {
    // flex: 1;
    box-sizing: border-box;
    padding: 0 20rpx;
    // margin-top: 0;
    // height: 100%;
    .recomm-scroll {
        display: flex;
        flex-direction: column;
        width: 100%;
        // margin: 20rpx 0 0;
        // height: calc(76vh - 100rpx);
        .list-item {
            background: #ffffff;
            // width: calc(100% - 40rpx);
            // height: 100%;
            padding: 0 20rpx;
            padding-bottom: 13rpx;
            margin-bottom: 10rpx;
            border-radius: 0px 0px 6rpx 6rpx;
            .title-bg {
                width: calc(100% + 40rpx);
                height: 24rpx;
                background: linear-gradient(0deg, #ffffff 0%, #faf8f2 100%);
                border-radius: 6rpx 6rpx 0px 0px;
                border: 2rpx solid #fff;
                box-sizing: border-box;
                margin: 0 -20rpx;
            }
            .item-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 35rpx;
                .item-title-left {
                    font-weight: bold;
                    font-size: 26rpx;
                    color: #1a1818;
                }
            }
            .item-body {
                display: flex;
                justify-content: space-between;
                margin-bottom: 34rpx;
                .left-prop {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    .icon-point {
                        width: 6rpx;
                        height: 6rpx;
                        background: #d1c9ba;
                        border-radius: 50%;
                        margin-right: 13rpx;
                    }
                    .prop-label {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: #666363;
                    }
                }
                .right-ans {
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #1a1818;
                }
            }
            .item-bottom {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                .left-icon {
                    margin-right: 50rpx;
                }
                image {
                    width: 34rpx;
                    height: 34rpx;
                }
            }
        }
    }
}

.flex_jus_sb {
    justify-content: flex-end;
    .left {
        margin-right: 30rpx;
    }
}
</style>
