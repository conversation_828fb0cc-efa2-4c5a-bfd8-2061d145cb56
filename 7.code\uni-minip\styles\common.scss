.pt10 {
	padding-top: 10rpx;
}
.pt20 {
	padding-top: 20rpx;
}
.pt30 {
	padding-top: 30rpx;
}
.pb10 {
	padding-bottom: 10rpx;
}
.pb20 {
	padding-bottom: 20rpx;
}
.pb30 {
	padding-bottom: 30rpx;
}
.mt10 {
	margin-top: 10rpx;
}
.mt20 {
	margin-top: 20rpx;
}
.mt30 {
	margin-top: 30rpx;
}
.mb10 {
	margin-bottom: 10rpx;
}
.mb20 {
	margin-bottom: 20rpx;
}
.mb30 {
	margin-bottom: 30rpx;
}
.mr10 {
	margin-right: 10rpx;
}
.mr20 {
	margin-right: 20rpx;
}
.mr30 {
	margin-right: 30rpx;
}
textarea, .u-textarea textarea.u-textarea__field {
	font-family: initial;
	font-size: 26rpx;
}
input {
	font-family: initial;
	font-size: 28rpx;
}
.area-title {
	font-weight: bold;
	font-size: 30rpx;
	position: relative;
	padding-left: 21rpx;
	margin-left: 22rpx;
	&:before {
		display: block;
		content: '';
		width: 10rpx;
		height: 2rpx;
		background: #333030;
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
	}
}
.tap-style:active:not([disabled]) {
	opacity: .7;
}

.flex_jus_sb {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.bottom-bar-price {
	font-weight: 400;
	font-size: 26rpx;
	color: $tip-font-color;
	display: flex;
	align-items: center;
	gap: 30rpx;
	text {
		font-weight: bold;
		font-size: 32rpx;
		color: $sec-font-color;
	}
}
.divider {
	height: 20rpx;
	@include bgUrl("order_dashed_line.png");
}