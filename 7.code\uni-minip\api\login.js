
import { request } from '@/utils/request.js'
// /auth/getUserInfo
export const checkPhone = (data)=>{
  return request({
    url: 'auth/getUserInfo',
    method: 'post',
    data
  })
}
 
export const login = (data)=>{
  return request({
    url: 'auth/loginByWx',
    method: 'post',
    data
  })
}

export const getUserInfo = ()=>{
  return request({
    url: 'museum-app/wx-user/info',
    method: 'get'
  })
}

export const getWriteOffStatus = ()=>{
  return request({
    url: 'museum-app/order-admission/getPermission',
    method: 'get'
  })
}

export const getProtocol = (data)=>{
  return request({
    url: 'museum-app/app/noticeManage/userAgreement',
    method: 'get',
	data
  })
}