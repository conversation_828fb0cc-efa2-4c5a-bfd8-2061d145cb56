<template>
	<custom-page bg="notice-bg.png">
	    <custom-header title="核销详情" bg-color="transparent"></custom-header>
		<view class="ticket" v-if="status!='4'&&status!='6'">
			<view class="ticket-title">
				<view class="tt-l"></view>
				<view class="tt-r">门票信息</view>
			</view>
			<view class="ticket-content">
				<view class="content" v-for="(item,index) in list">
					 <view class="c-l">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
				<view class="contents">
					<view class="cs-l">{{name}}</view>
					<view class="cs-r">{{value}}</view>
				</view>
			</view>
			<view class="ticket-end"></view>
		</view>
		<view class="tickets" v-if="status=='4'||status=='6'">
			<view class="ticket-title">
				<view class="tt-l"></view>
				<view class="tt-r">门票信息</view>
			</view>
			<view class="ticket-content">
				<view class="content" v-for="(item,index) in listFour">
					 <view class="c-l">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
			</view>
			<view class="ticket-end"></view>
		</view>
		<view class="info" v-if="status!='4'&&status!='6'">
			<view class="info-top">
				<view class="tt-l"></view>
				<view class="tt-r">游客信息</view>
			</view>
			<view class="info-mid">
				<view class="content" v-for="(item,index) in listTwo">
					 <view class="c-l" v-if="item.so==true"> <view class="circular"></view> <view class="text">{{item.name}}</view> </view>
					 <view class="c-l" v-if="item.so==false">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
			</view>
			<view class="info-bottom"></view>
		</view>
		<view class="ticketer">
			<view class="ticket-title">
				<view class="tt-l"></view>
				<view class="tt-r">核销信息</view>
			</view>
			<view class="ticket-content">
				<view class="content" v-for="(item,index) in listThree">
					 <view class="c-l">{{item.name}}</view>
					 <view class="c-r">{{item.value}}</view>
				</view>
			</view>
		</view>
	</custom-page>
</template>
<script>
  import {writeHisDetails} from "@/api/my.js"
  import { encryptIdCard, encryptPhone } from "@/utils";
	export default {
		data() {
			return {
				  list:[
					  {
						  name:'使用日期',
						  value:''
					  },
					  {
						  name:'场次时间',
						  value:''
					  },
					  {
						  name:'券码状态',
						  value:''
					  },
					  {
						  name:'订单编码',
						  value:''
					  }
				  ],
				  listFour:[
					  {
						  name:'门票来源',
						  value:''
					  },
					  {
						  name:'券码状态',
						  value:''
					  },
				  ],
				  listTwo:[
					  {
						  name:'游客姓名',
						  value:'迪丽扎哈',
						  so:false
					  },
					  {
					  	  name:'手机号',
					  	  value:'12312313123',
						  so:true
					  },
					  {
					  	  name:'身份证号',
					  	  value:'12312313123',
						  so:true
					  },
				  ],
				  listThree:[
					{
						name:'核销人员',
						value:''
					},
					{
						name:'核销时间',
						value:''
					},
					{
						name:'核销方式',
						value:''
					},
					{
						name:'入园码编号',
						value:''
					}
				  ],
				 name:'订单状态', 
				 value:'',
				 status:''
			};
		},
		onLoad(opt){
			if(opt&&opt.id){
				let params = {
					id:opt.id
				}
				writeHisDetails(params).then((res)=>{
					this.list[0].value = res.suitDate
					this.list[1].value = res.sessionDate
					this.list[2].value = res.statusText
					this.list[3].value = res.orderSn
					this.listTwo[0].value = res.contactsName
					this.listTwo[1].value = encryptPhone(res.contactsPhone)
					this.listTwo[2].value = encryptIdCard(res.contactsCredentialNo)
					this.listThree[0].value = res.verifincationName
					this.listThree[1].value = res.verifincationTime
					this.listThree[2].value = res.verifincationType
					this.listThree[3].value = res.verifincationSn
					this.value = res.orderStatusText
					this.listFour[0].value = res.orderSourceText
					this.listFour[1].value = res.statusText
					this.status = res.orderSource
				})
			}
		},
		methods:{
			
		}
	}
</script>
<style lang="scss" scoped>
.info{
	width: calc(100% - 40rpx);
	height: 330rpx;	
	margin-top: 20rpx;
	margin-left: 20rpx;
	.info-top{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
	.info-mid{
		width: 100%;
		height:180rpx;
		background-color: #FFFFFF;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				height: 50rpx;
				line-height: 50rpx;
				display: flex;
				align-items: center;
				.circular{
					width: 6rpx;
					height: 6rpx;
					background: #D1C9BA;
					border-radius: 50%;
					margin-right: 14rpx;
				}
				.text{
					font-weight: 400;
					font-size: 26rpx;
					color: $tip-font-color;
				}
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
	}
	.info-bottom{
		width: 100%;
		height: 30rpx;
		background: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
	}
}
.tickets{
	width: calc(100% - 40rpx);
	height: 270rpx;
	margin-left: 20rpx;
	margin-top: 20rpx;
	display: flex;
	flex-direction: column;
	.ticket-content{
		width: 100%;
		height:150rpx;
		background-color: #FFFFFF;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-top: 10rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
		.contents{
			width: 100%;
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			overflow: hidden;
			.cs-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				margin-top: -10rpx;
			}
			.cs-r{
				width: 118rpx;
				height: 52rpx;
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $sec-font-color;
				display: flex;
				justify-content: center;
				align-items: center;
				@include bgUrl("order_status_bg.png");
				background-size: 100% 100%;
			}
		}
	}
	.ticket-title{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
    .ticket-end{
		width: 100%;
		height: 30rpx;
		background-color: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
	}
}
.ticket{
	width: calc(100% - 40rpx);
	height: 470rpx;
	margin-left: 20rpx;
	margin-top: 20rpx;
	display: flex;
	flex-direction: column;
	.ticket-content{
		width: 100%;
		height:320rpx;
		background-color: #FFFFFF;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-top: 10rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
		.contents{
			width: 100%;
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			overflow: hidden;
			.cs-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				margin-top: -10rpx;
			}
			.cs-r{
				width: 118rpx;
				height: 52rpx;
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $sec-font-color;
				display: flex;
				justify-content: center;
				align-items: center;
				@include bgUrl("order_status_bg.png");
				background-size: 100% 100%;
			}
		}
	}
	.ticket-title{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
    .ticket-end{
		width: 100%;
		height: 30rpx;
		background-color: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
	}
}
.ticketer{
	width: calc(100% - 40rpx);
	height: 390rpx;
	margin-left: 20rpx;
	margin-top: 20rpx;
	display: flex;
	flex-direction: column;
	.ticket-content{
		width: 100%;
		height:270rpx;
		background-color: #FFFFFF;
		border-radius: 0rpx 0rpx 6rpx 6rpx;
		.content{
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			margin-top: 10rpx;
			margin-bottom: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.c-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
			}
			.c-r{
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $main-font-color;
			}
		}
		.contents{
			width: 100%;
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			overflow: hidden;
			.cs-l{
				margin-left: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $tip-font-color;
				margin-top: -10rpx;
			}
			.cs-r{
				width: 118rpx;
				height: 52rpx;
				margin-right: 20rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: $sec-font-color;
				display: flex;
				justify-content: center;
				align-items: center;
				@include bgUrl("order_status_bg.png");
				background-size: 100% 100%;
			}
		}
	}
	.ticket-title{
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		display: flex;
		align-items: center;
		@include bgUrl('writeOff-titleBg.png');
		background-size: 100% 100%;
		.tt-l{
			width: 10rpx;
			height: 2rpx;
			background: #333030;
			margin-left:22rpx;
			margin-right: 13rpx;
		}
		.tt-r{
			height: 30rpx;
			line-height: 30rpx;
			font-weight: bold;
			font-size: 30rpx;
			color: $main-font-color;
		}
	}
}
</style>