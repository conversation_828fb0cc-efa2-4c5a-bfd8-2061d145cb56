<template>
	<view class="custom-number-box">
		<template v-if="value > 0">
			<view class="btn reduce tap-style" @click="handleUpdate(-1)"></view>
			<input v-model="curValue" type="number" disabled @input="onInput">
		</template>
		<view class="btn add tap-style" :class="{disabled: value === max || disabledAdd}" :disabled="value === max" @click="handleUpdate(1)"></view>
	</view>
</template>

<script>
	export default {
		name:"custom-number-box",
		props: {
			value: Number,
			max: Number,
			disabledAdd: Boolean,
			notTip: Boolean
		},
		data() {
			return {
				curValue: this.value
			};
		},
		watch: {
			value(val) {
				this.curValue = val
			}
		},
		methods: {
			handleUpdate(val) {
				if (val === -1 && this.value === 0 || val === 1 && this.value === this.max || this.disabledAdd && val === 1) {
				// if (val === -1 && this.value === 0 || val === 1 && this.value === this.max) {
					!this.notTip && this.showMaxTip()
					return
				}
				this.onInput({
					detail: {
						value: +this.value + val
					}
				})
			},
			onInput(e) {
				this.$nextTick(() => {
					if (e.detail.value > this.max) {
						this.showMaxTip()
					}
					const val = e.detail.value > this.max ? this.max : +e.detail.value
					this.curValue = val
					this.$emit('input', val)
				})
			},
			showMaxTip() {
				uni.showToast({
					title: '已达最大数量',
					icon: 'none'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.custom-number-box {
		display: flex;
		align-items: center;
		gap: 7rpx;
		height: 40rpx;
		transform: translateX(13rpx);
		.btn {
			width: 50rpx;
			height: 50rpx;
			@include bgUrl("icon_add.png");
			background-position: center;
			background-size: 24rpx;
			&.reduce {
				@include bgUrl("icon_reduce.png");
				background-position: center;
				background-size: 24rpx;
			}
			&.disabled {
				opacity: .5;
			}
		}
		input {
			width: 88rpx;
			height: 40rpx;
			background: #F7F7F2;
			border-radius: 6rpx;
			text-align: center;
			padding: 0 10rpx;
			font-weight: bold;
			font-size: 28rpx;
		}
	}
</style>