<template>
    <view>
        <orderParty v-if="type==1" @type="changeType"></orderParty>
        <partyNumber v-if="type==2" @type="changeType" ref="partyNumber"></partyNumber>
    </view>
</template>

<script>
import orderParty from './orderParty.vue';
import partyNumber from './partyNumber.vue';
import {
    getQueue,
    getTemplateId

} from '@/api/party.js';
export default {
    data () {
        return {
            type: 2,
            openid: ''
        };
    },
    methods: {
        getQueueStatus () {
            let that = this;
            getQueue(1).then(res => {
                switch (res.queueStatus) {
                    case 0:
                        this.$refs.partyNumber.initData(res);
                        uni.showModal({
                            content: '您已预约排队，请不要重复预约',
                            showCancel: false,
                            confirmText: "知道了",
                            success: function (res) { }
                        });
                        this.type = 2;
                        break;
                    case 1:
                        uni.showModal({
                            content: '排队号码已过期，是否重新排队',
                            confirmText: "是",
                            cancelText: "否",
                            success: function (res) {

                                if (res.confirm) {
                                    that.type = 1;
                                } else if (res.cancel) {
                                    that.type = 2;
                                    that.$refs.partyNumber.selectTab(1);
                                }
                            }
                        });
                        break;
                    case 2:
                        this.type = 1;
                        break;
                }
            });
        },
        changeType () {
            this.type = this.type == 1 ? 2 : 1;
            if (this.type == 2) {
                this.$nextTick(() => {
                    this.$refs.partyNumber.initData();
                });
            }
        }
    },
    components: {
        orderParty,
        partyNumber
    },
    onLoad (option) {
        // if(option.scene=="partyCode"){
        // 	uni.setStorageSync("scene","partyCode")
        // }
        // else{
        // 	uni.removeStorageSync("scene")
        // }
        this.getQueueStatus();
    }
}
</script>

<style>
</style>