<template>
    <view class="custom-modal" :catchtouchmove="true">
        <u-popup :show="visible" round="6rpx" mode="center" :safeAreaInsetBottom="false" overlayOpacity="0.4"
            :closeOnClickOverlay="false" @close="close">
            <view class="custom-modal_inner">
                <view class="title">{{ title }}</view>
                <view class="body">
                    <scroll-view class="recomm-scroll" :scroll-y="true" :scroll-top="scrollTop">
                        <view class="second-title">
                            <view class="title-label">购买说明</view>
                            <view class="title-line">
                                <view class="left-line"></view>
                                <view class="right-line"></view>
                            </view>
                        </view>
                        <view class="label-content">{{info.purchaseInfo}}</view>
                        <view class="second-title">
                            <view class="title-label">使用说明</view>
                            <view class="title-line">
                                <view class="left-line"></view>
                                <view class="right-line"></view>
                            </view>
                        </view>
                        <view class="label-content">{{info.usageInfo}}</view>
                        <view class="second-title">
                            <view class="title-label">其他说明</view>
                            <view class="title-line">
                                <view class="left-line"></view>
                                <view class="right-line"></view>
                            </view>
                        </view>
                        <view class="label-content">{{info.otherInfo}}</view>
                    </scroll-view>
                </view>
            </view>
            <custom-button class="menu" @tap="onConfirm">{{cancelText}}</custom-button>
        </u-popup>
    </view>
</template>

<script>
import { getNotice } from "@/api/ticket.js";

export default {

    name: "custom-modal",
    props: {
        title: {
            type: String,
            default: '购票须知'
        },
        cancelText: {
            type: String,
            default: '知道了'
        },
    },
    mounted () {
        this.init();
    },
    data () {
        return {
            info: {
                purchaseInfo: "",//购买说明
                usageInfo: "",//使用说明
                otherInfo: "",//其他说明
            },
            visible: false,
            scrollTop: 0
        };
    },
    methods: {
        close () {
            this.visible = false;
        },
        showPop () {
            this.visible = true;
        },
        onCancel () {
            this.$emit("cancel");
            this.close();
        },
        onConfirm () {
            this.$emit("confirm");
            this.close();
        },
        // 获取须知内容
        init () {
            getNotice().then((res) => {
                if (res) {
                    this.info = res;
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-popup__content {
    position: relative;
    //底部按钮
}
.menu {
    width: 220rpx;
    height: 130rpx;
    position: absolute;
    bottom: -248rpx;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}
.recomm-scroll {
    height: calc(40vh);
}
.custom-modal {
    &_inner {
        width: calc(100vw - 40rpx);
        padding: 40rpx 30rpx;
        box-sizing: border-box;
        @include bgUrl('order_bg_title.png');
        .title {
            display: flex;
            justify-content: center;
            height: 35rpx;
            font-weight: bold;
            font-size: 36rpx;
            color: #1a1818;
            margin-bottom: 42rpx;
        }

        .body {
            display: flex;
            flex-direction: column;
            .second-title {
                display: flex;
                flex-direction: column;
                margin-top: 30rpx;
                .title-label {
                    font-weight: bold;
                    font-size: 28rpx;
                    color: #1a1818;
                    margin-bottom: 17rpx;
                }
                .title-line {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20rpx;
                    .left-line {
                        width: 50rpx;
                        height: 5rpx;
                        background: #b36859;
                    }
                    .right-line {
                        height: 1rpx;
                        width: 100%;
                        background: #d0c9bc;
                    }
                }
            }
            .label-content {
                white-space: pre-wrap;
            }
        }
        .bottom-bar {
            display: flex;
            justify-content: flex-end;
            gap: 30rpx;
            padding-top: 22rpx;
            height: 56rpx;
        }
    }
}
</style>