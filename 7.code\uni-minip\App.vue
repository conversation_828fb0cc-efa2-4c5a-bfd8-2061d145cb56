<script>
import { GBLN_CONFIG } from '@/utils/guobaoln'
	export default {
		onLaunch: function(options) {
			console.log('App Launch', options)
			this.handleSourceInfo(options)
			this.loadFontFace()
			if (this.$store.state.token) {
				this.$store.dispatch("GET_USER_INFO")
			}
		},
		onShow: function(e) {
			console.log('App Show', e)
			if (e.scene === 1089) {
				// 仅当小程序被关闭时，才切换到首页
				uni.removeStorageSync('guobaoln')
				uni.removeStorageSync('sourceInfo')
				uni.switchTab({
					url: '/pages/pages-tabs/home/<USER>'
				});
			}
			// 扫码进入的需要处理source信息
			// this.handleSourceInfo(e)
		},
		methods: {
			// 封装处理source信息的方法
			handleSourceInfo(options) {
				// options.query.source请存到全局和本地缓存，用于后续区分国宝辽宁的来源
				let source = options.query ? options.query.source : null; // 默认使用直接的source参数
				const isAiFace = !!(options.query.fakeFileName && options.query.realFileName)

				
				if (options.query && options.query.q) {
					const q = decodeURIComponent(options.query.q) // 获取到二维码原始链接内容
					console.log('q', q)
					
					// 从二维码URL中解析source参数
					try {
						// 使用正则表达式解析URL参数
						const sourceMatch = q.match(/[?&]source=([^&]*)/);
						if (sourceMatch && sourceMatch[1]) {
							source = decodeURIComponent(sourceMatch[1]); // 如果二维码中有source参数，使用它
							console.log('从二维码中解析到的source:', source);
						}
					} catch (error) {
						console.log('解析二维码URL失败:', error);
					}
				}
				
				// 只有当source有值时才更新存储
				uni.setStorageSync('guobaoln', source === 'guobaoln')
				if (isAiFace) {
					uni.setStorageSync('aiFace', isAiFace)
					uni.setStorageSync('aiFaceInfo', options.query)
				}
				const sourceInfo = {
					source: source,
					...GBLN_CONFIG
				}
				uni.setStorageSync('sourceInfo', sourceInfo)
				this.$store.commit('SET_SOURCE_INFO', sourceInfo)
			},
			loadFontFace() {
				wx.loadFontFace({
				    family: 'songti',
				    source: 'url("https://sdmp.jinhuaze.com/css_otf/app/js/SourceHanSerifCN-Regular.ttf")',
				    scopes: ['webview', 'native'],
					global: true,
				    complete: (res) => {
				        console.log('加载字体', res.status);
				    },
				    fail: function (res) {
				        console.log('加载字体失败' + res.status);
				    }
				});
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "@/uni_modules/uview-ui/index.scss";
	page {
		background-color: $main-color;
		font-family: 'songti';
		color: $main-font-color;
		font-size: 26rpx;
	}
</style>
