<template>
	<custom-page>
		<custom-header :title="isView ? '退款进度' : '申请退款'"></custom-header>
		<view class="page-container">
			<view class="refund-tips">
				订单申请退款后将无法恢复，经核实未使用且符合退改规则，预计<text>1-3工作日</text>原路返回至您的支付账户，退款金额以实际退款金额为准
			</view>
			<view class="expected-refund mt20">
				<view class="top flex_jus_sb">
					<view>预计可退</view>
					<view>￥ {{ details.refundAmount }}</view>
				</view>
				<div class="tip">预计1-15个工作日原路退回至原支付账户</div>
			</view>
			<!-- TODO：多种退款状态，联调的时候注意。 -->
			<template v-if="!isView || refundDetails.showTicketInfo">
				<view class="divider"></view>
				<view class="ticket-list">
					<view v-for="item in details.orderAdmission" :key="item.admissionId" class="ticket-item flex_jus_sb">
						<view>{{ item.admissionName }} <custom-number>{{ item.buyNum }}</custom-number></view>
						<view class="price">￥{{ item.admissionPrice }}</view>
					</view>
					<!-- <view class="ticket-item flex_jus_sb">
						<view>[嫦娥六号]单人早鸟票 <custom-number>2</custom-number></view>
						<view class="price">￥15.00</view>
					</view> -->
				</view>
			</template>
			
			<template v-if="isView">
				<view class="divider"></view>
				<view class="refund-steps">
					<u-steps :current="stepsCurrent" direction="column" dot :active-color="SEC_FONT_COLOR" inactiveColor="#D0C9BC">
						<u-steps-item v-for="(item, index) in details.refundProgressList" :key="index">
							<view slot="desc" class="slot-desc">
								<view class="status">{{ item.refundProgressName || refundStepsMap[item.refundProgress].title }}</view>
								<view class="desc desc_text">
									<text v-if="item.refundProgress === 4">不通过原因：{{ item.reason || '-' }}</text>
									<text v-else>{{ refundStepsMap[item.refundProgress].desc }}</text>
								</view>
								<view class="desc">{{ item.addTime }}</view>
							</view>
							<text class="slot-icon" :class="{active: index === current}" slot="icon"></text>
						</u-steps-item>
					</u-steps>
				</view>
			</template>
			
			<template v-if="!isView">
				<view class="refund-reason mt20">退款原因（单选必填）</view>
				<view class="reason-list">
					<radio-group @change='radioChange'>
						<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in details.refundReasonList" :key="item.id">
							<view class="radio-item">
								<view>
									<radio :value="item.refundReason" :checked="index === current" @change="onRadioChange" />
									<view class="custom-radio"></view>
								</view>
								<view>{{item.refundReason}}</view>
							</view>
						</label>
					</radio-group>
				</view>
			</template>
		</view>
		<custom-bottombar v-if="!isView" align-right>
			<custom-button type="primary" @click="onSubmit">提交</custom-button>
		</custom-bottombar>
	</custom-page>
</template>

<script>
	import { SEC_FONT_COLOR } from '@/utils/config';
	import { checkCancelOrder, orderRefund, refundProgress } from '@/api/order';
	
	export default {
		data() {
			return {
				SEC_FONT_COLOR,
				orderSn: null,
				isView: false, // 是否是查看退款进度
				requestDetails: {
					orderAdmission: [],
					orderAdmissionDetail: [],
					refundReasonList: []
				},
				refundDetails: {
					refundProgressList: []
				},
				current: 0,
				refundStepsMap: {
					1: {
						title: '提交',
						desc: '您的退款申请已提交，请等待退款审核，我们将快马加鞭为您处理'
					},
					2: {
						title: '审核中',
						desc: '您的退款申请审核中，请耐心等待'
					},
					3: {
						title: '通过',
						desc: '预计1-15个工作日退回至原支付账户'
					},
					4: {
						title: '驳回',
						desc: '不通过原因：XXXXXXX'
					},
					5: {
						title: '退款成功',
						desc: '退款成功，钱款已退回至原支付账户'
					}
				},
				stepsCurrent: 0,
				reasonValue: ''
				
			};
		},
		computed: {
			details() {
				return this.isView ? this.refundDetails : this.requestDetails
			}
		},
		onLoad({ orderSn, isView }) {
			console.log(orderSn, isView)
			this.orderSn = orderSn
			this.isView = !!isView
			const fn = this.isView ? this.getRefundProgress : this.init
			fn()
		},
		methods: {
			async init() {
				const data = await checkCancelOrder({ orderSn: this.orderSn })
				this.requestDetails = data
				this.reasonValue = data.refundReasonList[0].refundReason
			},
			// 退款进度
			async getRefundProgress() {
				const data = await refundProgress({ orderSn: this.orderSn })
				this.refundDetails = data
			},
			radioChange(e) {
				this.reasonValue = e.detail.value
			},
			async onSubmit() {
				if (!this.reasonValue) {
					uni.showToast({
						title: '请选择退款原因',
						iocn: 'none'
					})
					return
				}
				uni.showLoading({
					title: '请稍后...'
				})
				const { code, msg } = await orderRefund({ orderSn: this.orderSn, reason: this.reasonValue })
				code === 200 && uni.showToast({
					title: msg || '申请退款成功',
					icon: 'none'
				})
				this.isView = true
				this.getRefundProgress()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.border {
		border: 2rpx solid #FFFFFF;
	}

	.bg {
		background: linear-gradient(#FAF8F2 0%, #FFFFFF 100%);
	}

	.page-container {
		padding: 20rpx;
	}

	.refund-tips {
		padding: 26rpx;
		border-radius: 6rpx;
		@extend .border;
		@extend .bg;
		text {
			color: $sec-font-color;
			font-weight: bold;
		}
	}

	.expected-refund {
		border-radius: 6rpx 6rpx 0rpx 0rpx;
		@extend .border;
		@extend .bg;
		padding: 30rpx 20rpx;

		.top {
			height: 50rpx;
			line-height: 50rpx;

			>view:last-child {
				font-weight: bold;
				font-size: 32rpx;
				color: $sec-font-color;
			}
		}

		.tip {
			color: $tip-font-color;
			line-height: 30rpx;
		}
	}

	.ticket-list {
		padding: 10rpx 20rpx 40rpx;
		border-radius: 0 0 6rpx 6rpx;
		background-color: #fff;
	}

	.ticket-item {
		min-height: 70rpx;
		gap: 60rpx;
		font-size: 28rpx;

		>view:first-child {
			display: flex;
			align-items: center;
			gap: 13rpx;
		}

		.price {
			font-weight: bold;
			font-size: 28rpx;
		}
	}

	.refund-reason {
		height: 120rpx;
		line-height: 120rpx;
		padding-left: 20rpx;
		@extend .border;
		@extend .bg;
		border-radius: 6rpx 6rpx 0rpx 0rpx;
		font-size: 30rpx;
	}

	.reason-list {
		background-color: #fff;
		padding: 20rpx 20rpx 30rpx;
		border-top: 1rpx solid #D0C9BC;
		.radio-item {
			// border: 1rpx solid red;
			display: flex;
			align-items: center;
			gap: 20rpx;
			height: 70rpx;
			.custom-radio {
				width: 26rpx;
				height: 26rpx;
				background: #EBE9E2;
			}
			radio {
				display: none;
				&[checked]+.custom-radio {
					background: $sec-font-color;
					position: relative;
					&:before {
						display: block;
						content: '';
						width: 10rpx;
						height: 10rpx;
						background-color: #fff;
						border-radius: 50%;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}
			}
		}
	}
	.refund-steps {
		background-color: #fff;
		padding: 38rpx 40rpx 65rpx;
		line-height: 1;
		.slot-desc {
			padding-top: 4rpx;
			padding-left: 10rpx;
			.status {
				font-weight: bold;
				font-size: 30rpx;
			}
			.desc {
				font-size: 24rpx;
				color: $tip-font-color;
				line-height: 30rpx;
				&.desc_text {
					padding: 16rpx 0;
				}
			}
		}
		::v-deep .u-steps-item:not(:last-child) {
			.slot-desc {
				padding-bottom: 85rpx;
			}
		}
		.slot-icon {
			width: 28rpx;
			height: 28rpx;
			background: #F1EFEA;
			border-radius: 50%;
			position: relative;
			&:before {
				display: block;
				content: '';
				position: absolute;
				width: 15rpx;
				height: 15rpx;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				border-radius: 50%;
				background: #D0C9BC;
			}
			&.active {
				background: #DEC292;
				&:before {
					background: #B36859;
				}
			}
		}
	}
</style>