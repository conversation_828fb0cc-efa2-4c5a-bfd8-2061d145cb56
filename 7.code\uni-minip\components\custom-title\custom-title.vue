<template>
	<view class="custom-title flex_jus_sb" :style="{
		background: bgType ? `url(${IMG_URL + bgList[bgType]}) no-repeat center top` : '',
		backgroundSize: '100% auto'
		}">
		<view class="area-title flex_jus_sb">
			<slot></slot>
		</view>
		<view class="right">
			<slot name="right"></slot>
		</view>
	</view>
</template>

<script>
	import { IMG_URL } from '@/utils/config'
	export default {
		name:"custom-title",
		props: {
			bgType: String
		},
		data() {
			return {
				IMG_URL,
				bgList: {
					'1': 'order_bg_title.png',
					'2': 'order_area_title_bg.png'
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.custom-title {
	height: 120rpx;
	padding-right: 20rpx;
}
</style>