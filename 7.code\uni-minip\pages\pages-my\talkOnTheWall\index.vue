<template>
    <custom-page bg="bg_head_pro.png" :empty="porList.length === 0">
        <custom-header title="我的作品" :scroll-top="scrollTop" :scroll-ratio="2"></custom-header>
        <view class="body-list">
            <view class="list-item" v-for="(item,index) in porList" :key="index">
                <view class="item-bg">
                    <image @click="checkItem(item)" class="bg-img" :src="item.imgUrl"></image>
                    <!-- <view
                        :class="item.status==0?'item-left-tip yellow':item.status==1?'item-left-tip green':item.status==2?'item-left-tip red':'item-left-tip'">
                        {{item.status==0?'审核中':item.status==1?'审核通过':item.status==2?'审核不通过':''}}</view> -->
                    <view class="item-right-check" v-if="isManaged">
                        <checkbox-group @change="onCheckboxChange($event,item)">
                            <label>
                                <checkbox :value='1' :checked="item.agreeStatus"></checkbox>
                                <view class="custom-check"></view>
                            </label>
                        </checkbox-group>
                    </view>
                    <view class="bottom-time">
                        <view class="time-label">{{item.createTime}}</view>

                    </view>
                </view>
            </view>
        </view>
        <custom-bottombar v-if="porList.length > 0">
            <view class="flex_jus_sb">
                <custom-button type="info" class="mr30" @click="exitManage" v-if="isManaged">退出管理</custom-button>
                <custom-button type="info" class="mr30" @click="deleteItem" v-if="isManaged"
                    :disabled="selectArray.length === 0">删除</custom-button>
                <custom-button type="primary" @click="saveUser" v-if="isManaged"
                    :disabled="selectArray.length === 0">保存本地</custom-button>
                <custom-button type="primary" @click="isManaged=true"
                    v-if="!isManaged&&porList.length !== 0">管理作品</custom-button>
            </view>
        </custom-bottombar>
        <check-pro :imgUrl="imgUrl" ref="itemCheck"></check-pro>
        <custom-modal :visible.sync="showVisible" confirmText="删除" :title="modalTtile"
            @confirm="confirm"></custom-modal>
    </custom-page>
</template>


<script>
import { getMyPro, saveMyPro, deleteWork } from "@/api/my.js";
import { UPLOAD_IMG_URL } from '@/utils/config';
import CheckPro from './components/checkPro.vue';

export default {
    components: { CheckPro },
    data () {
        return {
            scrollTop: 0,
            porList: [],//作品数据
            imgUrl: '',
            selectArray: [],//选中图片
            isManaged: false,//管理作品
            modalTtile: '确认删除所选作品？',//
            ids: [],//删除作品
            showVisible: false,
            isMyGo: false,//是否通过我的页面跳转，否则为扫码跳转，扫码跳转需要先加载保存接口，后走列表接口。
            scene: '',//扫码参数
        };
    },
    onPageScroll ({ scrollTop }) {
        this.scrollTop = scrollTop;
    },
    mounted () {
        if (!this.isMyGo) {
            // 先走保存接口，后走列表接口。
            this.initSave();
        } else {
            // 直接走列表接口。
            this.init();
        }
    },
    onLoad (option) {
        console.log(option, '扫码进入我的作品页面参数：');
        // 存scene缓存 判断是否从我的作品进入
        if (option.scene) uni.setStorageSync('sceneMy', option.scene);
        console.log(uni.getStorageSync('sceneMy'), '扫码进入我的作品页面参数：');
        this.scene = option.scene || uni.getStorageSync('sceneMy') || '';
        this.isMyGo = option.isMyGo ? option.isMyGo : false;
    },
    methods: {
        // 查看大图
        checkItem (item) {
            this.imgUrl = item.imgUrl;
            this.$refs.itemCheck.showPop();
        },
        // 扫码跳转到我的作品，先走保存接口
        initSave () {
            saveMyPro(this.scene).then((res) => {
                console.log(res, './保存我的作品');

                if (res) {
                    this.init();
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
        // 获取我的作品
        init () {
            getMyPro().then((res) => {
                if (res) {
                    this.porList = res.rows.map(item => {
                        item.agreeStatus = false;
                        item.imgUrl = UPLOAD_IMG_URL + item.imgUrl;

                        return item;
                    });
                } else {
                    uni.$u.toast(res.msg);
                }

            });
        },
        // 确认删除
        confirm () {
            const parmasId = { workId: JSON.stringify(this.ids) };
            console.log(this.ids, '././23123123');
            deleteWork(this.ids).then((res) => {
                if (res) {
                    uni.showToast({
                        title: '删除成功',
                        icon: 'none'
                    });
                    this.init();
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
        // 删除作品
        deleteItem () {
            console.log(this.selectArray, './选中的数组');
            this.ids = this.selectArray.map(item => item.id);
            this.showVisible = true;
        },
        // 保存到本地相册
        saveUser () {
            let videolength = this.selectArray.length; // 要下载的总条数
            uni.showLoading({
                title: '图片下载中',
                mask: true
            });
            let that = this;
            let index = 0;
            for (let i = 0; i < this.selectArray.length; i++) {
                uni.downloadFile({
                    url: that.selectArray[i].imgUrl,
                    success: function (res) {
                        var temp = res.tempFilePath;
                        uni.saveImageToPhotosAlbum({
                            filePath: temp,
                            success (res1) {
                                index++;
                                // 全部下载完后触发
                                if (index == videolength) {
                                    uni.hideLoading();
                                    uni.$u.toast('保存成功');
                                }
                            },
                        });
                    }
                });
            }
        },
        // 全部下载完后触发调用
        successFun () {
            console.log('全部下载完后触发调用');
        },
        // 多选框
        onCheckboxChange (e, item) {
            // this.agreeStatus = e.detail.value.length > 0;
            item.agreeStatus = !item.agreeStatus;
            if (item.agreeStatus) {
                if (this.selectArray.indexOf(item) == -1) {
                    this.selectArray.push(item);
                }
            } else {
                this.selectArray.filter((element, index) => {
                    if (element.id == item.id) {
                        this.selectArray.splice(index, 1);
                    }
                });
            }
            console.log(this.selectArray, '.已选中的图片数组');
        },
        exitManage () {
            this.isManaged = false;
            this.porList.forEach(item => {
                item.agreeStatus = false;
            });
            this.selectArray = [];
        }
    }
}
</script>

<style lang="scss" scoped>
.page-container {
    padding: 0 20rpx;
}

.custom-check {
    width: 32rpx;
    height: 32rpx;
    background: #f1efea;
    border: 2rpx solid #b5a9a0;
    border-radius: 50%;
    margin-right: 10rpx;
    margin-top: 6rpx;
}
label {
    display: flex;
}
checkbox {
    display: none;
    &[checked] {
        & + .custom-check {
            @include bgUrl('icon_check_big.png');
            pointer-events: none;
            border-color: transparent;
        }
    }
}
.flex_jus_sb {
    justify-content: flex-end;
    .left {
        margin-right: 30rpx;
    }
}
.body-list {
    display: grid;
    grid-template-columns: repeat(2, 50%);
    grid-column-gap: 10rpx;
    justify-content: space-between;
    align-content: space-between;
    // width: calc(100% - 40rpx);
    margin: 0 20rpx;
    margin-top: 20rpx;
    .list-item {
        display: flex;
        // justify-content: center;
        .item-bg {
            width: 345rpx;
            height: 529rpx;
            @include bgUrl('pro_bgc.png');
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin-bottom: 20rpx;
            .bg-img {
                width: 315rpx;
                height: 500rpx;
                border: 2rpx solid #d0c9bc;
                // background: #b36859;
                border-radius: 6rpx;
            }
            .item-left-tip {
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                top: 25rpx;
                left: 25rpx;
                height: 32rpx;
                padding: 0 10rpx;
                border-radius: 2rpx;
                font-size: 22rpx;
                &.green {
                    background: #7b9d7c;
                    color: #ffffff;
                }
                &.yellow {
                    background: linear-gradient(0deg, rgba(218, 186, 139, 0.8) 0%, #f7dfc1 100%);
                    color: #1a1818;
                }
                &.red {
                    background: #b36859;
                    color: #ffffff;
                }
            }
            .item-right-check {
                position: absolute;
                top: 21rpx;
                right: 25rpx;
            }
            .bottom-time {
                position: absolute;
                bottom: 15rpx;
                right: 25rpx;
                left: 25rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                width: calc(100% - 50rpx);
                height: 50rpx;
                background: linear-gradient(0deg, rgba(218, 186, 139, 0.8) 0%, #f7dfc1 100%);
                border-radius: 6rpx 6rpx 0px 0px;
                .time-label {
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #1a1818;
                }
            }
        }
    }
}
</style>
