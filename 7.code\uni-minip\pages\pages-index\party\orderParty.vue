<template>
	<custom-page bg="lineup-bg.png">
	    <custom-header title="预约排队" bg-color="transparent" :navheight.sync="navHeight"></custom-header>
		<view class="lineup-top">
			 <view class="lineupT-left">
				 <view class="img"></view>
			 </view>
			 <view class="lineupT-right">
				 <view class="name">{{info.name}}</view>
				 <view class="number">
					 <view class="number-num">排队人数</view>
					 <view class="number-box">
						 <u-number-box v-model="value" @change="valChange" :max="3"></u-number-box>
					 </view>
				 </view>
			 </view>
		</view>
	    <view class="content">
	    	<view class="top"></view>
			<view class="contentbox">
				<view class="title">*游戏时间约为10分钟，叫号未到场需重新排队。</view>
				<view class="title">*排队人数上限为3人。</view>
				<view class="line"></view>
				<view class="checkbox">
					<checkbox-group class="checkbox-group" @change="onCheckboxChange">
				     	<checkbox class="abc" :value='1' :checked="agreeStatus"></checkbox>
						<view class="test"></view>
					</checkbox-group>
					
					<text class="text">阅读并同意以下内容</text>
				</view>
		        <view class="instructions">
		        	<view class="instructions-title">一、不适用人群</view>
					<view class="instructions-text" v-for="(item,index) in contentlist" :key="index">
						{{item.name}}
					</view>
					<view class="instructions-title" style="margin-top: 20rpx;">二、免责条款</view>
					<view class="instructions-text" v-for="(item,index) in disclaimer" :key="index">
						{{item.name}}
					</view>
		        	
		        </view>
			</view>
	    </view>
		<view class="bottom">
			<view class="button" @click="ok">确认预约</view>
		</view>
	</custom-page>
</template>

<script>
    import {vrSave,getTemplateId} from "@/api/party.js"
	
	export default {
		data() {
			return {
				navHeight:0,
				info:{
					img:'https://img2.baidu.com/it/u=3565369971,2082314928&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=800',
					name:'《梦回大唐：奇幻时空之旅》',					
				},
				value: 1,
				agreeStatus:false,
				contentlist:[
					{name:'健康限制：孕妇;'},
					{name:'患有心脏病、高血压、脑血管疾病患者;'},
					{name:'癫痫或有癫痫病史者;'},
					{name:'近期接受过手术或身体有严重外伤者;'},
					{name:'视力严重受损或有眼疾者;'},
					{name:'有严重平衡系统障碍者;'},
					{name:'有精神病史或心理承受能力较弱者;'},
					{name:'饮酒或服用药物后感觉不适者;'},
					{name:'其他可能因VR体验而引发健康问题的个体。'},
					{name:'行为能力限制：无法理解或遵守安全指南的个体。'},
				],
				disclaimer:[
					{name:'健康声明：参与者再参与本VR游乐项目前,必须确保自身健康状况适合参与此类活动,并已充分了解可能的健康风险。'},
					{name:'遵守规则：参与者必须遵守所有安全操作规程和工作人员的知道。对于不遵守规定而导致的任何损失或伤害,本场馆不承担责任。'},
					{name:'免责声明：参与者明确了解VR体验可能带来的不适感,包括但不限于眩晕、恶心、恐慌等。场馆已采取必要措施以确保参与者的安全,但对于参与者因个人健康状况或反应而产生的任何不适或健康问题,场馆不承担任何责任。'},
					{name:'财产安全：请妥善保管个人物品,对于再体验过程中因个人疏忽造成的财产损失,场馆不承担赔偿责任。'},
					{name:'行为规范：参与者不得在体验过程中进行任何可能危害自己或他人安全的行为,如有违反,场馆有权立即终止其体验,并保留追究责任的权力。'},
					{name:'未成年人参与：13周岁以下未成年人参与本项目须由成年人陪同,并由陪同的成年人对未成年人的安全负责。'},
					{name:'免责声明的接受：参与者在参与本VR游乐项目前,必须阅读并同意上述免责条款。参与者的参与行为将被视为对上述条款的接受和认可。'},
				],
				openid:[]
				
				
			};
		},
		filters:{
			
		},
		methods:{
			valChange(e) {
			},
			onCheckboxChange(v){
				this.agreeStatus = !this.agreeStatus
			},
			ok(){
				if(this.agreeStatus == false){
					uni.$u.toast('请勾阅读并同意以下内容')
				}else if(this.agreeStatus == true){
					let params = {
						number:this.value
					}
					vrSave(params).then((res)=>{
						if(res.id != null){
							uni.$u.toast('预约成功')
							this.push()
							setTimeout(()=>{
								this.$emit('type')
							},1000)
						}
					})
				}
			},
			push(){
				let that = this
				getTemplateId().then(res=>{
					that.openid = res
					//通知弹窗
					uni.requestSubscribeMessage({
					    tmplIds: [that.openid],
					    complete (res) {
					       console.log(res,'aaa');
					    }
					});
				})
			},
		},
		onLoad(){
		
		}
	}
</script>

<style lang="scss" scoped>
.lineup-top{
	width: calc(100% - 40rpx);
	height: 220rpx;
	margin-left: 20rpx;
	margin-bottom: 20rpx;
	@include bgUrl('lineup-box.png');
	background-size: 100% 100%;
	display: flex;
	justify-content: space-between;
	.lineupT-left{
		width: 160rpx;
		height: 160rpx;
		margin: 32rpx 20rpx 28rpx 20rpx;
		.img{
			width: 160rpx;
			height: 160rpx;
			@include bgUrl('lineup-picture.png');
			background-size: 100% 100%;				
		}
	}
	.lineupT-right{
		flex: 1;
		margin-top: 50rpx;
		margin-bottom: 36rpx;
		margin-right: 40rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		.name{
			font-family: Source Han Serif CN;
			font-weight: bold;
			font-size: 30rpx;
			color: #1A1818;
			margin-left: -16rpx;
		}
		.number{
			height: 40rpx;
			line-height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.number-num{
				font-family: Source Han Serif CN;
				font-weight: bold;
				font-size: 30rpx;
				color: #1A1818;
			}
		}
	}
}
.content{
	width: calc(100% - 40rpx);
	margin-left: 20rpx;
	margin-bottom: 180rpx;
	border-radius: 6rpx;
	overflow: hidden;
	.top{
		width: 100%;
		height: 24rpx;
		background: linear-gradient(0deg, #FFFFFF 0%, #FAF8F2 100%);
		border-top: 2rpx solid #FFFFFF;
		box-sizing: border-box;
	}
	.contentbox{
		background-color: #FFFFFF;
		padding-bottom: 40rpx;
		.instructions{
			width: calc(100% - 50rpx);
			margin-left: 30rpx;
			.instructions-title{
				font-size: 24rpx;
				font-family: SourceHanSerifCN-Bold;
				font-weight: bold;
				color: rgba(102, 99, 99, 1);
				margin-bottom: 20rpx;
			}
			.instructions-text{
				line-height: 42rpx;
			}
		}
		.title{
			height: 30rpx;
			line-height: 30rpx;
			font-family: Source Han Serif CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #B36859;
			margin-left: 20rpx;
			margin-bottom: 26rpx;
		}
		.line{
			width: calc(100% - 40rpx);
			margin-left: 20rpx;
			margin-bottom: 30rpx;
			border: 1rpx dashed #D0C9BC;
			box-sizing: border-box;
			margin-bottom: 30rpx;
		}
		.checkbox{
			// height: 28rpx;
			// line-height: 28rpx;
			margin-left: 20rpx;
			display: flex;
			align-items: center;
			position: relative;
			margin-bottom: 30rpx;
			.text{
				margin-left: 20rpx;
				font-family: Source Han Serif CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #1A1818;
			}
			.test {
				width: 28rpx;
				height: 28rpx;
				border: 2rpx solid #4D3B2E;
				border-radius: 50%;
				position: relative;
				box-sizing: border-box;
				
			}
			.abc {
				width: 28rpx !important;
				height: 28rpx !important;
				border-radius: 50%;
				position: absolute;
				left: 0;
				top: 0;
				z-index: 999;
				opacity: 0;
				&[checked]+.test {
					width: 28rpx !important;
					height: 28rpx !important;
					background:  #4D3B2E;			
					&:before {
						display: block;
						content: '';
						width: 3rpx;
						height: 16rpx;
						background: #fff;
						position: absolute;
						right: 8rpx;
						top: 6rpx;
						transform: rotate(45deg);
					}
					&::after {
						display: block;
						content: '';
						width: 3rpx;
						height: 11rpx;
						background: #fff;
						position: absolute;
						left: 6rpx;
						top: 11rpx;
						transform: rotate(-45deg);
					}
				}
			}
		}	
	} 
}
.bottom{
	width: 100%;
	height: 180rpx;
	background: #FCFBF7;
	display: flex;
	justify-content: center;
	position: fixed;
	bottom: 0;
	overflow: hidden;
	.button{
		width: calc(100% - 60rpx);
		height: 80rpx;
		margin-top: 16rpx;
		background: linear-gradient(0deg, #DABA8B 0%, #F7DFC1 100%);
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 34rpx;
		color: #1A1818;
	}
}
</style>