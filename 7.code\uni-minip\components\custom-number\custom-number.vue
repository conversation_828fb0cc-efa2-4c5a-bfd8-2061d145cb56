<template>
	<view class="custom-number">
		<!-- <text>×</text> -->
		<text><slot>0</slot></text>
	</view>
</template>

<script>
	export default {
		name:"custom-number"
	}
</script>

<style lang="scss" scoped>
	.custom-number {
		// line-height: 1;
		// line-height: 0;
		height: 40rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: $tip-font-color;
		display: flex;
		align-items: center;
		
		padding-left: 30rpx;
		@include bgUrl("x.png");
		background-position: left 58%;
		background-size: 22rpx;
		// background-color: #ccc;
		/* text {
			line-height: 0;
			font-size: 37rpx;
			margin-right: 5rpx;
			font-family: initial;
			color: #848080; // 显示的效果会比较黑，所以特意调淡一点。
		} */
		text {
			line-height: 0;
		}
	}
</style>