<template>
    <custom-page bg="bg_notice.png">
        <custom-header title="展厅简介" :scroll-top="scrollTop" :scroll-ratio="2"></custom-header>
        <view class="content">
            <view class="content-bg">
                <view class="top-title">{{info.exhibitionName}}</view>
                <view class="top-loc">
                    <image mode=scaleToFill :src="IMG_URL+'icon_Loc.png'">
                    </image>
                    <view class="loc-label">{{info.exhibitionAddress}}</view>
                </view>
            </view>
            <view class="content-bottom">
				<u-parse :content="info.announcement"></u-parse>
            </view>
        </view>
    </custom-page>
</template>

<script>
import { getNotice } from "@/api/ticket.js";
import { IMG_URL } from '@/utils/config';

export default {
    components: {},
    data () {
        return {
            scrollTop: 0,
            IMG_URL,
            info: {
                exhibitionAddress: '',//展厅地址
                exhibitionName: '',//展厅名称
                announcement: '',//图文介绍
            },

        };
    },
    onPageScroll ({ scrollTop }) {
        this.scrollTop = scrollTop;
    },
    mounted () {
        this.init();
    },
    methods: {
        // 获取须知内容
        init () {
            getNotice().then((res) => {
                console.log(res, '././././');
                if (res) {
                    this.info = res;
                } else {
                    uni.$u.toast(res.msg);
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.page-container {
    padding: 0 20rpx;
}
.radius {
    border-radius: 6rpx;
    overflow: hidden;
}
.content-bg {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(100% - 70rpx);
    margin: 0 20rpx;
    padding: 30rpx 0;
    padding-left: 30rpx;
    // height: 80rpx;
    background: linear-gradient(0deg, #ffffff 0%, #faf8f2 100%);
    border-radius: 6rpx 6rpx 0px 0px;
    border: 2rpx solid #ffffff;
    .top-title {
        font-weight: bold;
        font-size: 30rpx;
        color: #1a1818;
        margin-bottom: 25rpx;
    }
    .top-loc {
        display: flex;
        align-items: center;
        image {
            width: 22rpx;
            height: 28rpx;
            margin-right: 10rpx;
        }
    }
}
.content-bottom {
    width: calc(100% - 44rpx);
    margin: 0 20rpx;
    height: 100%;
    background: #ffffff;
    border-radius: 6rpx;
    border: 4rpx solid #ffffff;
}
</style>
