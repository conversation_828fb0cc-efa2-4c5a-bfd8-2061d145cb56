<template>
    <view class="custom-checkbox">
        <checkbox-group @change="onChange">
            <label>
                <checkbox :value="value" :checked="checked" :disabled="disabled"></checkbox>
                <view class="custom-check"></view>
            </label>
        </checkbox-group>
        <slot></slot>
    </view>
</template>

<script>
export default {
    name: 'custom-checkbox',
    props: {
        value: {
            type: [String, Number],
            default: '1'
        },
        checked: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {};
    },
    methods: {
        onChange(e) {
            this.$emit('change', e);
        }
    }
}
</script>

<style lang="scss">
.custom-checkbox {
    display: flex;
    align-items: center;
    
    label {
        display: flex;
    }
    
    .custom-check {
        width: 32rpx;
        height: 32rpx;
        background: #f1efea;
        border: 2rpx solid #b5a9a0;
        border-radius: 50%;
        margin-right: 10rpx;
        margin-top: 6rpx;
    }
    
    checkbox {
        display: none;
        &[checked] {
            & + .custom-check {
                @include bgUrl('icon_check_big.png');
                pointer-events: none;
                border-color: transparent;
            }
        }
    }
}
</style> 