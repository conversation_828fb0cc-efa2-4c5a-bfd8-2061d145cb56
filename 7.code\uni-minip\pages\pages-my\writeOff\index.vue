<template>
	<custom-page bg="notice-bg.png">
	    <custom-header title="门票核销" bg-color="transparent"></custom-header>
		<view class="top">
			<view class="top-left">
				<view class="tl-t">
					<view class="tlt-l"></view>
					<view class="tlt-r">累计核销</view>
				</view>
				<view class="tl-b">{{info.num}}</view>
			</view>
			<view class="top-right">
				<view class="tl-t">
					<view class="tlt-l"></view>
					<view class="tlt-r">今日核销</view>
				</view>
				<view class="tl-b">{{info.today}}</view>
			</view>
		</view>
		<view class="bottom">
			<view class="bottom-left">
				<view class="bl-l"></view>
				<view class="bl-r">核销记录</view>
			</view>
			<view class="bottom-right" @click="look">立即查看</view>
		</view>
		<custom-button class="menu" @tap="writeOff">立即核销</custom-button>
	</custom-page>
</template>
<script>

	import {writeNum,writeCode} from "@/api/my.js"
	export default {
		data() {
			return {
			   info:{
				   num:'',
				   today:''
			   },
			   datas:null
			};
		},
		onLoad(){
			this.writeNum();
		},
		methods:{
			writeNum(){
				writeNum().then((res)=>{
					this.info.num = res.sum
					this.info.today = res.today
				})
			},
			writeOff(){
				this.scanCode()
				
			},
			//扫一扫
			scanCode() {
				// 允许从相机和相册扫码
				uni.scanCode({
					// scanType: ['QR_CODE'], //条形码
					success: function(res) {
						// ('条码类型：' + res.scanType);
						// ('条码内容：' + res.result);
						// 微信小程序
						if (res.errMsg == "scanCode:ok") {
							let params = {
								res: res.result
							}
							// 扫描到的信息
							writeCode(params).then((res)=>{
								this.datas = res
								uni.navigateTo({
									url:'/pages/pages-my/writeOff/details?datas='+ encodeURIComponent(JSON.stringify(this.datas))
								})
							})							
						} 
					}
				});
			},
			look(){
				uni.navigateTo({
					url:'/pages/pages-my/writeOff/record'
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
.top{
	width: calc(100% - 60rpx);
	height: 370rpx;
	display: flex;
	justify-content: space-between;
	margin: 30rpx;
	.top-left{
		flex: 1;
		margin-right:15rpx;
		@include bgUrl('writeOff-left.png');
		background-size: 100% 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		.tl-b{
			margin-left: 40rpx;
			font-weight: bold;
			font-size: 36rpx;
			color: $sec-font-color;
		}
		.tl-t{
			display: flex;
			align-items: center;
			margin-left: 40rpx;
			height: 40rpx;
			line-height: 40rpx;
			.tlt-l{
				width: 5rpx;
				height: 30rpx;
				line-height: 30rpx;
				background: #4D3B2E;
				margin-right: 20rpx;
			}
			.tlt-r{
				height: 40rpx;
				line-height: 40rpx;
				font-weight: bold;
				font-size: 34rpx;
				color: $main-font-color;
			}
		}
	}
	.top-right{
		flex: 1;
		margin-left:15rpx;
		@include bgUrl('writeOff-right.png');
		background-size: 100% 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		.tl-b{
			margin-left: 40rpx;
			font-weight: bold;
			font-size: 36rpx;
			color: $sec-font-color;
		}
		.tl-t{
			display: flex;
			align-items: center;
			margin-left: 40rpx;
			height: 40rpx;
			line-height: 40rpx;
			.tlt-l{
				width: 5rpx;
				height: 30rpx;
				line-height: 30rpx;
				background: #4D3B2E;
				margin-right: 20rpx;
			}
			.tlt-r{
				height: 40rpx;
				line-height: 40rpx;
				font-weight: bold;
				font-size: 34rpx;
				color: $main-font-color;
			}
		}
	}
}
.bottom{
	width: calc(100% - 60rpx);
	height: 190rpx;
	margin-left: 30rpx;
	@include bgUrl('writeOff-bottom.png');
	background-size: 100% 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.bottom-left{
		margin-left: 35rpx;
		height: 40rpx;
		line-height: 40rpx;
		display: flex;
		align-items: center;
		.bl-l{
			width: 5rpx;
			height: 30rpx;
			line-height: 30rpx;
			background: #4D3B2E;
			margin-right: 20rpx;
		}
		.bl-r{
			height: 40rpx;
			line-height: 40rpx;
			font-weight: bold;
			font-size: 34rpx;
			color: $main-font-color;
		}
	}
	.bottom-right{
		margin-right: 40rpx;
		width: 150rpx;
		height: 56rpx;
		background: #FAF9F4;
		border-radius: 8rpx;
		border: 1px solid #D1C9BA;
		box-sizing: border-box;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: bold;
		font-size: 26rpx;
		color: #4D3B2E;

	}
}
.menu{
	width: 100%;
	display: flex;
	justify-content: center;
	position: fixed;
	bottom: 80rpx;
}
</style>


