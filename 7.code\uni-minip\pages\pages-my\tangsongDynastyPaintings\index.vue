<template>
	<custom-page bg="bg_head_pro.png">
		<custom-header title="唐宋画境" :scroll-top="scrollTop" :scroll-ratio="2"></custom-header>
		<view class="tshj_list">
			<view v-for="(item, idx) in list" :key="idx" class="tshj_item-wrapper">
				<custom-swipe-item
					:index="idx"
					:disabled="showCheckbox"
					:delete-button-width="80"
					:delete-button-top="16"
					:delete-button-height="200"
					@click="onItemClick"
					@delete="showDeleteModal"
					@swipe-start="onSwipeStart"
					ref="swipeItems"
				>
					<view class="tshj_item">
						<view class="tshj_item-avatarbox">
							<image :src="item.status == 0 ? item.lockCostumeUrl : item.unlockCostumeUrl" mode="widthFix"></image>
						</view>
						<view class="tshj_item-content">
							<view class="title">唐宋画境 <view v-if="item.status != 0" class="tag">无水印</view>
							</view>
							<view class="row">
								<view class="label">场景</view>
								<view class="value">{{ item.sceneName }}</view>
							</view>
							<view class="row">
								<view class="label">服饰</view>
								<view class="value">{{ item.costumeName }}</view>
							</view>
							<view class="row">
								<view class="label">创建时间</view>
								<view class="value">{{ item.createTime }}</view>
							</view>
						</view>
						<view class="tshj_item-checkbox" v-if="showCheckbox">
							<custom-checkbox :checked="checkedList.includes(idx)"
								@change="onCheckboxChange(idx, $event)"></custom-checkbox>
						</view>
					</view>
				</custom-swipe-item>
			</view>
		</view>
		<custom-bottombar>
			<view class="flex_jus_sb">
				<custom-button v-if="!showCheckbox" type="primary" @click="toggleManage">{{ '管理作品' }}</custom-button>
				<view v-else class="manage-btns">
					<custom-button type="info" @click="toggleManage">退出管理</custom-button>
					<custom-button type="primary" :disabled="!checkedList.length" @click="onDeleteClick">删除</custom-button>
				</view>
			</view>
		</custom-bottombar>
		<custom-modal :visible.sync="showModal" title="删除后无法恢复，请谨慎操作！" confirmText="删除" @cancel="showModal = false"
			@confirm="onModalConfirm"></custom-modal>
	</custom-page>
</template>

<script>
import CustomCheckbox from '@/components/custom-checkbox/custom-checkbox.vue';
import CustomModal from '@/components/custom-modal/custom-modal.vue';
import CustomSwipeItem from '@/components/custom-swipe-item/custom-swipe-item.vue';
import { getAiFaceList, deleteAiFace } from '@/api/aiFace.js'
import { handleSaveAiFace } from "@/utils/businessLogic.js"
export default {
	components: { CustomCheckbox, CustomModal, CustomSwipeItem },
	data() {
		return {
			scrollTop: 0,
            showCheckbox: false,
            checkedList: [],
            showModal: false,
			list: [],
			deleteIndex: null, // 要删除的项目索引
			isAiFace: false,
			aiFaceInfo: null,
		};
	},
	onLoad() {
		this.isAiFace = uni.getStorageSync('aiFace')
		this.aiFaceInfo = uni.getStorageSync('aiFaceInfo')
		console.log(this.isAiFace, this.aiFaceInfo)
		if (this.isAiFace) {
			this.initSaveAiFace(this.aiFaceInfo)
			return
		}
		this.init()
	},
	onShow() {
		if (!this.isAiFace) {
			this.init()
		}
	},
	methods: {
		async initSaveAiFace({ dressId, fakeFileName, realFileName, type}) {
			await handleSaveAiFace({
				costumeId: dressId,
				unlockCostumeUrl: realFileName,
				lockCostumeUrl: fakeFileName,
				type
			})
			await this.init()
		},
		async init() {
			const data = await getAiFaceList()
			this.list = data
		},
		toggleManage() {
			this.showCheckbox = !this.showCheckbox;
            if (!this.showCheckbox) {
                this.checkedList = [];
            }
		},
        onCheckboxChange(idx, e) {
            const checked = e.detail.value.length > 0;
            if (checked) {
                if (!this.checkedList.includes(idx)) {
                    this.checkedList.push(idx);
                }
            } else {
                this.checkedList = this.checkedList.filter(i => i !== idx);
            }
        },
        onDeleteClick() {
            this.deleteIndex = null; // 批量删除
            this.showModal = true;
        },
        async onModalConfirm() {
			console.log(this.deleteIndex, this.list[this.deleteIndex])
            if (this.deleteIndex !== null) {
                // 单个删除
				const { code } = await deleteAiFace({ ids: this.list[this.deleteIndex].id })
				code === 200 && this.init()
                // 删除后关闭所有滑动状态
                this.closeAllSwipeItems();
				uni.showToast({
					title: '删除成功',
					icon: 'none'
				})
            } else {
                // 批量删除
				const ids = this.checkedList.map(i => this.list[i].id).join(',')
				const { code } = await deleteAiFace({ ids })
				code === 200 && this.init()
                this.checkedList = [];
				uni.showToast({
					title: '删除成功',
					icon: 'none'
				})
            }
            this.showModal = false;
            this.deleteIndex = null;
        },
		// 显示删除确认弹窗
		showDeleteModal(index) {
			this.deleteIndex = index;
			this.showModal = true;
		},
		// 滑动开始时关闭其他已打开的项目
		onSwipeStart(currentIndex) {
			if (this.$refs.swipeItems) {
				this.$refs.swipeItems.forEach((item, index) => {
					if (index !== currentIndex) {
						item.close();
					}
				});
			}
		},
		// 关闭所有滑动项的状态
		closeAllSwipeItems() {
			this.$nextTick(() => {
				if (this.$refs.swipeItems) {
					this.$refs.swipeItems.forEach((item) => {
						if (item && typeof item.close === 'function') {
							item.close();
						}
					});
				}
			});
		},
		// 修改:点击item切换选中状态或跳转详情页
		onItemClick(idx) {
			if (this.showCheckbox) {
				const isChecked = this.checkedList.includes(idx);
				if (isChecked) {
					this.checkedList = this.checkedList.filter(i => i !== idx);
				} else {
					this.checkedList.push(idx);
				}
			} else {
				// 跳转到详情页
				uni.navigateTo({
					url: '/pages/pages-my/tangsongDynastyPaintings/detail?faceId=' + this.list[idx].id
				});
			}
		},
	},
	onPageScroll({ scrollTop }) {
		this.scrollTop = scrollTop;
	},
}
</script>

<style lang="scss" scoped>
.tshj_list {
	margin-top: 50rpx;
	padding: 0 20rpx;
	.tshj_item-wrapper {
		height: 232rpx;
		&+.tshj_item-wrapper {
			margin-top: 10rpx;
		}
	}
	.tshj_item {
		height: 232rpx;
		display: flex;
		align-items: center;
		position: relative;
		z-index: 2;
		&-avatarbox {
			height: 100%;
			width: 164rpx;
			position: absolute;
			@include bgUrl("tshj_list-avatar_bg.png");
			display: flex;
			align-items: center;
			justify-content: center;
			image {
				width: 133rpx;
				height: 200rpx;
				border: 4rpx solid #D0C9BC;
				border-radius: 10rpx;
			}
		}
		&-content {
			height: 200rpx;
			flex: 1;
			@include bgUrl("tshj_list-item_bg.png");
			padding-left: calc(164rpx + 31rpx);
			display: flex;
			flex-direction: column;
			justify-content: center;
			.title {
				font-weight: bold;
				font-size: 28rpx;
				color: #1A1818;
				display: flex;
				align-items: center;
				padding-bottom: 8rpx;
				.tag {
					// width: 106rpx;
					padding: 5rpx 20rpx;
					background: #7B9D7C;
					border-radius: 2rpx;
					font-size: 22rpx;
					color: #FFFFFF;
					height: 32rpx;
					line-height: 1;
					display: flex;
					align-items: center;
					margin-left: 11rpx;
				}
			}
			.row {
				display: flex;
				font-size: 24rpx;
				.label {
					width: 155rpx;
					color: #666363;
				}
				.value {
					flex: 1;
					color: #1A1818;
				}
			}
		}
        &-checkbox {
            margin-left: 20rpx;
            display: flex;
            align-items: center;
            height: 100%;
			position: absolute;
			right: 20rpx;
			top: 50%;
			transform: translateY(-50%);
			// 添加:阻止事件冒泡，避免重复触发
			pointer-events: none;
        }
	}
}
.flex_jus_sb {
	justify-content: flex-end;
}
.manage-btns {
	display: flex;
	gap: 20rpx;
}


</style>