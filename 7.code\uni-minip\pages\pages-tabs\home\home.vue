<template>
	<custom-page :catchtouchmove="true" :safe-area-inset-bottom="false">
		<custom-header bg="bg_headSmall.png" title="辽博数字展厅" :navheight.sync="navHeight" :placeholder="false" bg-for-nav
			bg-color="#F7F7F2" v-show="isShowAll" animation title-position-left></custom-header>
		<view class="video-background" :style="{'height':`calc(100vh - ${tabbarHeight}px)`}" @touchstart="touchStart"
			@touchend="touchEnd">
			<!-- 视频背景 -->
			<video v-if="indexForm.fileType==2" class="video-content" :src="UPLOAD_IMG_URL + indexForm.fileUrl" autoplay
				loop :muted="isMuted"></video>
			<image v-else class="video-content" :src="UPLOAD_IMG_URL + (indexForm.fileUrl || defaultBanner)">
			</image>
			<view :style="{ 'top':`${tophight}rpx`}" class="top-content">
				<view class="mute-icon">
					<image mode=scaleToFill :src="IMG_URL+'icon_voiceNo.png'" v-if="isMuted" @click="changeMut(true)">
					</image>
					<image mode=scaleToFill :src="IMG_URL+'icon_voice_2.png'" v-else @click="changeMut(false)"></image>
				</view>
				<view class="tooltip-content">
					<image mode=scaleToFill :src="IMG_URL+'icob_notice.png'">
					</image>
					<u-notice-bar bgColor="transparent" :text="newNotice" color="#FFFFFF"></u-notice-bar>
				</view>
				<view class="title-content">
					<view class="left-chin">辽 博 数 字 展 厅</view>
					<view class="left-Egn">D I G I T A L A R T</view>
				</view>
			</view>
			<!-- 页面其他内容 -->
			<view class="content">
				<view class="button" @click="goOrder">
					<image mode=scaleToFill :src="IMG_URL+'button_order.png'"></image>
				</view>
				<view class="silde-button">
					<image mode=scaleToFill :src="IMG_URL+'icon_up.png'"></image>
				</view>
			</view>
			<view class="bottom-back" @click="showHalf()">
				<image mode=scaleToFill :src="IMG_URL+'pic_bottomlitNew.png'"></image>
			</view>
		</view>
		<!-- 滑动半屏card -->
		<view class="index-card" :catchtouchmove="true">
			<popup ref="popupCard" :showToatal.sync="isShowAll" :nav-height="navHeight" :tabbar-height="tabbarHeight"
				:indexListArr="indexListArr" @getList="init" :total="total">
			</popup>
		</view>
		<tab-bar :value="0" :tabbar-height.sync="tabbarHeight"></tab-bar>
	</custom-page>
</template>

<script>
	import {
		indexNotice,
		indexList,
		indexVideo
	} from '@/api/home.js';
	import popup from './component/popup.vue';
	import {
		IMG_URL,
		UPLOAD_IMG_URL
	} from '@/utils/config';
	import {
		checkLogin
	} from '@/utils';

	export default {
		data() {
			return {
				touchSatartX: 0,
				touchSatartY: 0,
				IMG_URL, //图片路径
				UPLOAD_IMG_URL, //获取pc图片地址
				titleBarHeight: 44,
				tophight: "", //顶部高度
				statusBarHeight: '',
				isShowAll: false, //是否全屏展示
				navHeight: 0,
				tabbarHeight: 0,
				isMuted: false, //是否静音
				newNotice: '',
				duration: 24000,
				scrollWidth: '100%',
				indexListArr: [], // 精彩推荐列表、
				total: 0,
				videoUrl: '', //视频背景地址
				indexForm: {
					fileType: '1',
					fileUrl: ''
				}, //首页展示内容
				defaultBanner: 'asset/indexHome.png'
			};
		},
		onLoad(opts) {
			console.log('scene =========>', opts.scene)
			if (opts.scene) {
				try {
					const scene = decodeURIComponent(opts.scene)
					const obj = {};
					
					scene.replaceAll('"', '').split(',').forEach(item => {
						console.log(item)
						const [key, value] = item.split('=')
						obj[key] = value
					})
					console.log(obj)
					uni.setStorageSync('sourceType', obj.sourceType)
				} catch {}
			}
			
			// this.init();
			this.notice();
			// #ifdef MP-WEIXIN
			let menuButtonObject = uni.getMenuButtonBoundingClientRect();
			// #endif
			this.statuBarHeight = uni.getSystemInfo({
				success: res => {
					// #ifdef MP-WEIXIN
					let navHeight = menuButtonObject.height + (menuButtonObject.top - res
						.statusBarHeight) * 2;
					this.titleBarHeight = navHeight;
					// #endif
					this.statusBarHeight = res.statusBarHeight;
				}
			});
			this.tophight = this.statusBarHeight + 88 + 40; //顶部位置
		},
		onShareAppMessage() {
			const promise = new Promise(resolve => {
				setTimeout(() => {
					resolve({
						title: '辽宁省博物馆数字展厅'
					})
				}, 2000)
			})
			return {
				title: '辽博数字展厅',
				path: '/pages/pages-tabs/home/<USER>',
				promise
			}
		},
		onShow() {
			this.indexListArr = []
			this.init();
		},
		components: {
			popup,
		},
		methods: {
			async notice() {
				const {
					noticeTitle
				} = await indexNotice();
				this.newNotice = noticeTitle;
			},
			async init(value) {
				let data = {
					pageNum: value ? value.pageNum : 1,
					pageSize: value ? value.pageSize : 15
				};
				const {
					rows,
					total
				} = await indexList(data);
				const data1 = await indexVideo();
				this.indexForm = data1;
				// this.indexListArr.push(...rows);
				//去重
				rows.forEach((element,index)=>{
					this.checkArr(element).then(res=>{
						if(res){
							this.indexListArr.push(element)
						}
					})
				})
				this.total = total;
			},
			checkArr(element){
				return new Promise((resolve,reject)=>{
					if(this.indexListArr.length>0){
						this.indexListArr.forEach((arrElement,arrIndex)=>{
							if(arrElement.id==element.id){
								resolve(false)
							}
							else if(arrIndex==this.indexListArr.length-1){
								resolve(true)
							}
						})
					}
					else{
						resolve(true)
					}
				})
			},
			// 修改静音
			changeMut(value) {
				if (value) {
					this.isMuted = false;
				} else {
					this.isMuted = true;
				}
			},
			// 立即订票
			goOrder() {
				if (!checkLogin()) return;
				console.log('立即订票');
				uni.navigateTo({
					url: '/pages/pages-index/ticketBooking/ticketBooking'
				});
			},
			// 点击底部四个图标或区域弹出半屏
			showHalf() {
				this.$refs.popupCard.open();
			},
			//  触摸开始
			touchStart(e) {
				// console.log("触摸开始", e);
				this.touchSatartX = e.touches[0].clientX;
				this.touchSatartY = e.touches[0].clientY;
			},
			// 触摸结束
			touchEnd(e) {
				// console.log("触摸结束", e);
				let deltaX = e.changedTouches[0].clientX - this.touchSatartX;
				let deltaY = e.changedTouches[0].clientY - this.touchSatartY;
				if (Math.abs(deltaY) > 50 && Math.abs(deltaX) < Math.abs(deltaY)) {
					if (deltaY < 0) {
						// console.log('上滑');
						this.$refs.popupCard.open();
					} else {
						// console.log('下滑');
						this.$refs.popupCard.close();

					}
				}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .u-transition {
		background-color: rgba(0, 0, 0, 0) !important;
	}

	// ::v-deep .u-notice-bar {
	//     background-color: transparent !important;
	// }
	::v-deep .u-icon__icon {
		display: none !important;
	}

	::v-deep .u-notice-bar {
		padding-top: 9rpx !important;
	}

	.top-content {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		z-index: 1;
		right: 0;
	}

	.video-background {
		display: flex;
		flex-direction: column;
		position: relative;
		width: 100%;
		height: calc(100vh - 84px);
		/* 视频背景高度设置为视窗高度 */
		overflow: hidden;

		.video-content {
			width: 100%;
			height: 100%;
			object-fit: cover;
			/* 确保视频覆盖整个容器 */
			position: absolute;
			top: 0;
			left: 0;
			background-color: black;
		}

		.content {
			position: absolute;
			z-index: 1;
			/* 确保内容在视频上方 */
			bottom: 200rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100%;

			.button {
				width: 220rpx;
				height: 130rpx;
				margin-bottom: 47rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.silde-button {
				width: 86rpx;
				height: 86rpx;

				image {
					width: 100%;
					height: 100%;
					animation: float 2s ease-in-out infinite;
				}
			}
		}

		@keyframes float {
			0% {
				transform: translateY(0);
			}

			50% {
				transform: translateY(-6px);
			}

			100% {
				transform: translateY(0);
			}
		}

		.bottom-back {
			position: absolute;
			z-index: 1;
			bottom: -10rpx;
			width: 100%;
			height: 190rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.mute-icon {
			position: absolute;
			top: 10rpx;
			right: 40rpx;
			z-index: 1;
			width: 48rpx;
			height: 38rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.tooltip-content {
			position: absolute;
			top: -0;
			left: 65rpx;
			z-index: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			width: calc(100% - 170rpx);

			image {
				width: 20rpx;
				height: 24rpx;
				margin-bottom: 5rpx;
			}

			.label-scroll {
				width: 500rpx;
				white-space: nowrap;
				letter-spacing: 4rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #ffffff;
				opacity: 0.8;
				margin-left: 30rpx;
			}
		}

		.title-content {
			position: absolute;
			top: 52rpx;
			left: 50rpx;
			z-index: 1;
			display: flex;
			justify-content: flex-start;
			align-items: flex-start;

			.left-chin {
				width: 40rpx;
				font-weight: 500;
				font-size: 40rpx;
				color: #ffffff;
			}

			.left-Egn {
				width: 15rpx;
				writing-mode: vertical-lr;
				/* 文字垂直排列，从左向右读 */
				direction: rtl;
				/* 设置文字方向为从右到左，实现左侧镜像 */
				word-spacing: 8rpx;
				font-size: 18rpx;
				color: #dec292;
				margin-left: 18rpx;
				margin-top: 14rpx;
			}
		}
	}

	// 半屏card
	.index-card {
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;

		.card-top {
			display: grid;
			grid-template-columns: repeat(4, 25%);
		}
	}
</style>