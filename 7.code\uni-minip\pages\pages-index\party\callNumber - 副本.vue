<template>
	<custom-page :bg-top="navHeight">
		<custom-header title="排队叫号" :navheight.sync="navHeight" show-loading></custom-header>
		<!-- <u-loading-icon class="loading"  :show="show" text="加载中" textSize="18"></u-loading-icon> -->
		<view class="lineup" v-if="list==true">
			<view class="left">
				<view class="name">《梦回大唐：奇幻时空之旅》</view>
				<view class="linep">
					<view class="linep-text">当前排号：</view>
					<view class="linep-num">{{number}}</view>
				</view>
				<view class="time">
					<view class="time-text">排队时间：</view>
					<view class="time-num">{{time}}</view>
				</view>
			</view>
			<view class="right" @click="clickAfter">下一位</view>
		</view>
		<u-popup :show="show" @close="close" mode="center" :round="7" >
			<view class="popbox">
				 <view class="pop-text">当前排队号码是最后一位！</view>
				 <view class="pop-button" @click="yes">我知道了</view>
			</view>
		</u-popup>
	</custom-page>
</template>

<script>
	import {getNowNumber,useAndGetNextNumber} from "@/api/party.js"
	export default {
		data() {
			return {
				list:false,
				show:false,
	            navHeight:0,
				content:'当前排队号码是最后一位！',
				number:'',
				time:'',
				id:''
			};
		},
		filters:{
		
		},
		onLoad(){
		  this.getNowNumber()
		},
		onShow(){

		},
		onReachBottom(){
			
		},
		onPullDownRefresh(v){
		   this.getNowNumber()
		},
		methods:{
			yes(){
				this.show = false
				this.getNowNumber()
			},
			getNowNumber(){
				getNowNumber().then(res=>{
					if(res.id == null){
						this.list = false
					}else{
						this.list = true
						this.number = res.numberText
						this.time = res.createTime
						this.id = res.id
						this.$forceUpdate()
					}
					
				})
			},
			clickAfter(){
				let params = {
					id:this.id
				}
				useAndGetNextNumber(params).then(res=>{
					if(res.id == null){
						this.show = true
					}else{
						this.number = res.numberText
						this.time = res.createTime
						this.id = res.id
						this.$forceUpdate()
					}
					
				})
			},
			close(){
				this.show = false
			}
		}
	}
</script>

<style lang="scss">
::v-deep .u-popup__content {
	width: 600rpx !important;
}
::v-deep .u-safe-bottom{
	display: none;
}
.popbox{
	display: flex;
	flex-direction: column;
	.pop-text{
		width: 100%;
		height: 200rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 30rpx;
		color: #1A1818;
		border-bottom: 1rpx solid #D0C9BC;
	}
	.pop-button{
		width: 100%;
		height: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 30rpx;
		color: #1A1818;
	}
}
.lineup{
	width: calc(100% - 40rpx);
	height: 200rpx;
	margin-left: 20rpx;
	@include bgUrl('feedback-box.png');
	background-size: 100% 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.left{
		margin-left: 30rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		.name{
			font-family: Source Han Serif CN;
			font-weight: bold;
			font-size: 28rpx;
			color: #1A1818;
			margin-bottom: 16rpx;
			margin-left: -16rpx;
		}
		.linep{
			height: 32rpx;
			line-height: 32rpx;
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;
			.linep-text{
				font-family: Source Han Serif CN;
				font-weight: 400;
				font-size: 22rpx;
				color: #666363;
				margin-right: 12rpx;
			}
			.linep-num{
				height: 32rpx;
				line-height: 32rpx;
				text-align: center;
				background: linear-gradient(0deg, rgba(218,186,139,0.8) 0%, #F7DFC1 100%);
				border-radius: 6rpx;
				font-family: Source Han Serif CN;
				font-weight: 400;
				font-size: 22rpx;
				color: #1A1818; 
				padding-left: 26rpx;
				padding-right: 26rpx;

			}
		}
		.time{
			display: flex;
			align-items: center;
			font-family: Source Han Serif CN;
			font-weight: 400;
			font-size: 22rpx;
			color: #666363;
			.time-text{
				margin-right: 10rpx;
			}
			.time-num{
				
			}
		}
	}
	.right{
		margin-right: 30rpx;
		width: 150rpx;
		height: 56rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(0deg, #DABA8B 0%, #F7DFC1 100%);
		border-radius: 8rpx;
		font-family: Source Han Serif CN;
		font-weight: bold;
		font-size: 26rpx;
		color: #1A1818;
		// border: 1px solid;
	}
}
</style>
