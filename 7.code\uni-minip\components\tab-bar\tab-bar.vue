<template>
	<view class="custom-tabbar">
		<u-tabbar :value="value" @change="onTabChange" :fixed="true" :placeholder="true"
			:safe-area-inset-bottom="true" activeColor="#1A1818" inactive-color="#666363" :border="false" z-index="9999999">
			<u-tabbar-item v-for="(tab, index) in tabs" :key="index" :text="tab.text">
				<image class="u-page__item__slot-icon" slot="active-icon" :src="require(`@/static/images/tabbar/${tab.activeIcon}`)"></image>
				<image class="u-page__item__slot-icon" slot="inactive-icon" :src="require(`@/static/images/tabbar/${tab.inactiveIcon}`)"></image>
			</u-tabbar-item>
		</u-tabbar>
	</view>
</template>

<script>
	import { checkLogin } from '@/utils';
	export default {
		props: {
			value: Number,
			tabbarHeight: Number
		},
		data() {
			return {
				tabs: [
					{
						text: '首页',
						id: 0,
						inactiveIcon: 'home_off.png',
						activeIcon: 'home_on.png',
						path: '/pages/pages-tabs/home/<USER>'
					},
					{
						text: '我的',
						id: 1,
						inactiveIcon: 'my_off.png',
						activeIcon: 'my_on.png',
						path: '/pages/pages-tabs/mine/mine'
					}
				]
			}
		},
		mounted() {
			this.getTabbarHeight()
		},
		methods: {
			getTabbarHeight() {
				uni.getSystemInfo({
				    success: (info) => {
					   this.$emit("update:tabbarHeight", info.safeAreaInsets.bottom + 50)
				    }
				});
			},
			onTabChange(index) {
				const curTab = this.tabs[index]
				// if (curTab.id === 1 && !checkLogin()) return
				uni.switchTab({
					url: curTab.path
				})
			}
		},
		options: {
			styleIsolation: "shared"
		}
	}
</script>

<style lang="scss">
	.custom-tabbar ::v-deep {
		.u-page__item__slot-icon {
			width: 58rpx!important;
			height: 58rpx!important;
		}
		.u-tabbar-item__icon {
			padding-top: 10rpx;
		}
		.u-tabbar-item__text {
			font-weight: bold;
			font-size: 22rpx;
			color: #1A1818;
		}
		.u-tabbar__content {
			background-color: $main-color!important;
		}
	}
</style>